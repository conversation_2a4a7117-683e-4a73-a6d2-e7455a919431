{"_id": "prebuild-install", "_rev": "58-c691f9732302d31f6c38b64fa603ca04", "name": "prebuild-install", "dist-tags": {"latest": "7.1.3"}, "versions": {"1.0.0": {"name": "prebuild-install", "version": "1.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "877263dbd54d232ef3daff184a094e7850b0040c", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-1.0.0.tgz", "integrity": "sha512-dPHr/2TiV9PNEUn7g1/VYC24PNrT8AL0y5kOUe2TlV14HAxekdRbm2tp5r9YNzs8Y1yAzL4eYAr1QEpvZPhStw==", "signatures": [{"sig": "MEYCIQDmSnfG1clMgAWhP0iGTVV9IQfKphC+csagfsvHmcVtUQIhAMFS2ISOWlQfZDAtuycWkSO6k98BKlPjofJCwrFVZ+P8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "877263dbd54d232ef3daff184a094e7850b0040c", "gitHead": "17abb4e7ba07796126aeb56a2d5c16e2ddca748e", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^2.0.3", "tar-fs": "^1.12.0", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.0.0", "noop-logger": "^0.1.1", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^7.1.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-1.0.0.tgz_1463496655603_0.5208812109194696", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "prebuild-install", "version": "1.0.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@1.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "839680714c3387538b4966c7566d4c58c022ac33", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-1.0.1.tgz", "integrity": "sha512-1OB6TtIVrIVRG708rL3Xd1eYdxIKW3vUi7nK3igtaP/YSvO4qierxQ0rNt3J/w+m64O7DAgS0rGGAUkc/VZOFg==", "signatures": [{"sig": "MEQCIDaXL7yxCXywcF9vKp3NxpWfsMTMKGmw51ZsmsJEdAmJAiBZi8JBISxLIHuZUzkdzS0zIJuf2g4kXS6yMFKKB9f23A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "839680714c3387538b4966c7566d4c58c022ac33", "gitHead": "111d8ee268dde0f4c85da8647bc6ac0c70ba0eed", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "6.1.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^2.0.3", "tar-fs": "^1.12.0", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.0.0", "noop-logger": "^0.1.1", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^7.1.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-1.0.1.tgz_1463519473256_0.0846379364375025", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.2": {"name": "prebuild-install", "version": "1.0.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@1.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "fe568314a59a3cc7972163a79488f152c6b14ba2", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-1.0.2.tgz", "integrity": "sha512-Jp0nH1kM5G/2EJ0Z8WjMegX4u5u/czbKrGQqA6WjG04vdZkbAxyG8ETqIk3CK5LjkjS1mjv/lE8ONaRvMtQlAg==", "signatures": [{"sig": "MEQCIHqivKRk32I96q7dzNhNlLe2Q2IGFkvEJp+dIn3+Hgd+AiA2rNLhhXefSg42gZCV5J0XVI3wfT8yItYnx5Lxz5CWDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "fe568314a59a3cc7972163a79488f152c6b14ba2", "gitHead": "cb4dd711a7a39ac45457b5b0e0f79bd6d75d54e3", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^2.0.3", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.0.0", "noop-logger": "^0.1.1", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^7.1.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-1.0.2.tgz_1464469719114_0.8564140002708882", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "prebuild-install", "version": "1.1.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@1.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "783cad04ce65a3001da73fb2999f7bb0dfee8e4b", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-1.1.0.tgz", "integrity": "sha512-uwlE17eHzXCk6cQBJoFn1BIigDOuGxSUjL7w8rlC6hoMKkTK/Kd9GV9D9QZk9385XLnyQME/uUpPO98zNIUAsA==", "signatures": [{"sig": "MEQCIDsb8uDJVS0j7IKg8536ea6H44THP2aVJEPMCd6SDIuvAiAGNEW/JPvN04ma0AhsI2WmN/NArWwwJpOnDANWoP2WAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "783cad04ce65a3001da73fb2999f7bb0dfee8e4b", "gitHead": "8517efc85cd74da9ec0a37562cf1adb20c0e8569", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "5.11.1", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^2.0.3", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.0.0", "noop-logger": "^0.1.1", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^7.1.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-1.1.0.tgz_1464604922412_0.267256414052099", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "prebuild-install", "version": "2.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6b194814a00a60a696e832c944e517c7bd72b1be", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.0.0.tgz", "integrity": "sha512-a/+6Xp35cVmAdIb8a0dQv6+vG0tijWP5rDQsr4q7+qQeh8bPv5a5Jdj+ZatvodevChnRke2dVbjMbinrNEim/Q==", "signatures": [{"sig": "MEUCIQDdtsxs9MiYxf5I54WXGoqoo438wCrVISBEdOO8iYvmtQIgNWlOxYkgNpP7SkqbQN/EIp1B8omTka67Y2cg1mmEHVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6b194814a00a60a696e832c944e517c7bd72b1be", "gitHead": "8091bc031c8bc674d344a43231f628d1bc8c39a4", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^1.0.3", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.4.3", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.0.0.tgz_1483019174814_0.33165694889612496", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.0": {"name": "prebuild-install", "version": "2.1.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "526c7b3ed1e2707a247f7c040719173a321bc14f", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.1.0.tgz", "integrity": "sha512-hqvCN7jdpCPS26VVMHdzD20clqlQwCyjVafKsWM3zYlssuwXeSeDo5zHSPGt3UGJUW3h6oz3PQXBZG1MLzEBSA==", "signatures": [{"sig": "MEYCIQCX5bid0KaUK9+Hssj2vnLuq6LhTkiYgL6TwS1umJDxhAIhAOIofsPQDtn6Fuv2wb+jd7GupJQEPtAgfJU/LkimSaY7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "526c7b3ed1e2707a247f7c040719173a321bc14f", "gitHead": "d80e30273dd4f1b828b58eb3c73bfd6bcd8a442f", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "piranna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "6.9.2", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^1.0.3", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.4.3", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.1.0.tgz_1483822917588_0.025979788741096854", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.1": {"name": "prebuild-install", "version": "2.1.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "d0a77ea51b6a00f928cb71bc0ccea24f87ec171e", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.1.1.tgz", "integrity": "sha512-XjNNz3prNUnBlcW72Y2OftguKfbXAx5E13Ntrpg5QCkfbXsk/tyBfm3sSDdMxvTgTbyWGpli2zixMobiscYd4g==", "signatures": [{"sig": "MEQCIHoUL7kjMT+J94wgxDYa+PHGP8eW97YaJMZC3hf9rxVCAiBpn1KYkKCqSMixog9FpSfAHoswSlaOQwG3bKP2m6JlUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d0a77ea51b6a00f928cb71bc0ccea24f87ec171e", "gitHead": "1024bb0e794ff13f1b31a7b2f6614b632c7c7e3f", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.0.0", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.4.3", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.1.1.tgz_1488477426320_0.6201443427708", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.2": {"name": "prebuild-install", "version": "2.1.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "d9ae0ca85330e03962d93292f95a8b44c2ebf505", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.1.2.tgz", "integrity": "sha512-x4RbdzzwVCDdkWiB0kgNTNUYxj0BWms2jQW1oZq6lIeamA4ZmgCwrSbWPV1tMkqCwefRqexgTi80CmAdT7OxLw==", "signatures": [{"sig": "MEUCIQC3bizO/HXG9xeEGdH8jepnbI+iaoAtEJPMfMHJ0H9XiAIgCMmRwPRKb3izoi/sG9LlYQgKCkLPRYsUCIiU385Qnaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d9ae0ca85330e03962d93292f95a8b44c2ebf505", "gitHead": "be380f3a5177dbe6412f8d089e784fd75c30206a", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.0.0", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.4.3", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.1.2.tgz_1491814178272_0.0412924790289253", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "prebuild-install", "version": "2.2.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.2.0", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "55934756a32bac8747390ca44ff663cee8b99b69", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.2.0.tgz", "integrity": "sha512-H3IGfcFUFQYuSa0rdH4v70PgNsup33/DZIUflHiStw2OUsIJ6Bims1TF367/wKAhsAsLnuGg4jU2wQOUwcdj9Q==", "signatures": [{"sig": "MEUCIDb/7aomkjSkJ211ckVzTf3WmbSiEmRenuTnjMN/HvQrAiEA0J1Qw+97g6VlEaW32JnDb7yqvxHYyBoyqEextPBPM98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "55934756a32bac8747390ca44ff663cee8b99b69", "gitHead": "2c3f3a26dd919abd63d837088de2bfc6a287e8c2", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.0.0", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.2.0.tgz_1499377722121_0.7436535509768873", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "prebuild-install", "version": "2.2.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.2.1", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "cdf16b041505bde8c55168abd7e58ea264e537ca", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.2.1.tgz", "integrity": "sha512-y/sgNJ49vjXQ3qYdSI/jTRZq6D7g5Q2euK6x0/L8dvwK1EGvNLidtg2t4PZzTgkR6LahkzpYVshOmHKYtp0AlQ==", "signatures": [{"sig": "MEQCIHUENcybbkzCRg4quFdQzm9SrS/XDMLoD+IYbHQXBk+uAiAuVdiMX0mUbH+FyrMXXWCcVXwj1gR/CL/5gXU4IUyIuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "0997322ff93c8483a245ef0443d5e2a6f1cd3137", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "8.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.0.0", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.2.1.tgz_1500724545440_0.10901290667243302", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "prebuild-install", "version": "2.2.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.2.2", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "dd47c4d61f3754fb17bbf601759e5922e16e0671", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.2.2.tgz", "integrity": "sha512-F46pcvDxtQhbV3B+dm+exHuKxIyJK26fVNiJRmbTW/5D7o0Z2yzc8CKeu7UWbo9XxQZoVOC88aKgySAsza+cWw==", "signatures": [{"sig": "MEQCIEGhfXQATodMRhNirRoBR6tllKXo7K2LtdWW0ZouPp2gAiAa2dbdQkDBPl2zdcppcztODkqX7sv0/5dEqhEu7/lnZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "456ecda8e0c8470ab0b9eddfac0001f01c32e459", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "8.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.0.0", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.2.2.tgz_1502277332113_0.3099504834972322", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "prebuild-install", "version": "2.3.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.3.0", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "19481247df728b854ab57b187ce234211311b485", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.3.0.tgz", "integrity": "sha512-gzjq2oHB8oMbzJSsSh9MQ64zrXZGt092/uT4TLZlz2qnrPxpWqp4vYB7LZrDxnlxf5RfbCjkgDI/z0EIVuYzAw==", "signatures": [{"sig": "MEQCIGs3dLHoaPHor0xIFMNO5S1x1iFM7llwXboKW+uItdszAiAL+3WbO17c4TDFj7c2gwqXT3LSe1adEhQlotRek/Doeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "4ca5b0001481ffaf1c3ec64340c1c107ad66feff", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.1.1", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.3.0.tgz_1507110378987_0.6229625081177801", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "prebuild-install", "version": "2.4.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.4.0", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "9cd6325847f984136dd993375395d420da270a75", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.4.0.tgz", "integrity": "sha512-2v/FFGgF8Z2UjmM7dHiEwfuldALnZZsBEfjY6Pk1wPu88gYexPkIBUJwHYJ7e/kasFu8zbtTPY5bY45Vc2MC6g==", "signatures": [{"sig": "MEYCIQD7Wmg4Hu7i84rdW1CdVSMA0jdLBftRAnlfYqkDTjL2xQIhAKhdyQ4U46kmQoCdEyGNqY10od1ZUPlpY9xC3sFPOTfn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "94a0211f841a1a0f92f11cebab104f50ac9c0802", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.1.1", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.4.0.tgz_1512561095247_0.572210049489513", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "prebuild-install", "version": "2.4.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.4.1", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "c28ba1d1eedc17fbd6b3229a657ffc0fba479b49", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.4.1.tgz", "integrity": "sha512-99TyEFYTTkBWANT+mwSptmLb9ZCLQ6qKIUE36fXSIOtShB0JNprL2hzBD8F1yIuT9btjFrFEwbRHXhqDi1HmRA==", "signatures": [{"sig": "MEYCIQCna4l2ZigFz3EG9rkimheW18xSt4/gJMQqEOwHAl6SMwIhAKmWUk5J/+eq8yUHxS7MO/lIwZd2/DfMtZCS6Rh0UE37", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "28c600eec7eebee811d7bfd048161d15c3688657", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.1.1", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.4.1.tgz_1512814941212_0.4735178924165666", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "prebuild-install", "version": "2.5.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.5.0", "maintainers": [{"name": "piranna", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6fdd8436069971c76688071f4847d4c891a119f4", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.5.0.tgz", "integrity": "sha512-3wlyZgmkeeyduOR8Ursu5gKr3yWAYObACa5aJOtt2farRRFV/+zXk/Y3wM6yQRMqmqHh+pHAwyKp5r82K699Rg==", "signatures": [{"sig": "MEUCIQCJ/oYETXVi8BJm4O3s/W3fjjM9YIZScsYzR0yup6vsiwIgCrXuc1MYJmskk3fUmCyCzaMREJ4RdHF4fvR01wbU6TE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "63721a8f7a8f6547cf72b8c85d59546252af2d37", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"rc": "^1.1.6", "pump": "^1.0.1", "xtend": "4.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.1.1", "os-homedir": "^1.0.1", "simple-get": "^1.4.2", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^8.6.0", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install-2.5.0.tgz_1516709378088_0.2365416418761015", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "prebuild-install", "version": "2.5.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.5.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "0f234140a73760813657c413cdccdda58296b1da", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.5.1.tgz", "fileCount": 13, "integrity": "sha512-3DX9L6pzwc1m1ksMkW3Ky2WLgPQUBiySOfXVl3WZyAeJSyJb4wtoH9OmeRGcubAWsMlLiL8BTHbwfm/jPQE9Ag==", "signatures": [{"sig": "MEQCIHLH+ExAmpXyPhin86Y+GnC4w5n5QSA9Ar15kqEiehAuAiATSSPKftnhgwz3vQt9tvw9PjH5s9DAAZANUt3VUrVh5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18959}, "gitHead": "00187177601745a2d9a6eb7bfa5c385ad976ca12", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"rc": "^1.1.6", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_2.5.1_1518132396777_0.9687993737822158", "host": "s3://npm-registry-packages"}}, "2.5.2": {"name": "prebuild-install", "version": "2.5.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.5.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "8bdfb6325b98610bfd6832c7d0074bf1b59da848", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.5.2.tgz", "fileCount": 14, "integrity": "sha512-/KVYCewcfs20kkwifbTAk+3LVr79h4+E+TQG+MtK26Yh632jUJcJOTy6MX4MtE6xHpsGMEtQ8oxDRVJH333dag==", "signatures": [{"sig": "MEUCIQD6Y76UIryWrbX9ct5nMEMGE/SPKnaJdBT68QpjhYZ6EAIgI+LyOsOiyl+BHq7xYev+6g2kjAbi7Q855bcfUUpA5vQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa09B1CRA9TVsSAnZWagAAsbMQAIYOVl9B22bLb89aUkDw\nHxmESGUw/Szs1Vb62HwTMOL9Cnn1bbs0HrryjJ4D2Erac4q1ODeJOgz+wBuE\n3bNFL+STdP9fWesKE7+IquD45fCJtvwbMEyd7tHd8MqssA2P3cxtMU9+XYAZ\nBn+VGJ7RHpYlRHBcOsau/uD5Xnpf022fa7zJHdEzmVDl9aVd1OLITarWNL3B\nN3JBEr+Bc+KdKy8JhZGzvUt6ctdBq232UHWXpS6/r8+blA8FjkQPfnAtImL2\nPU98EpAvPgOg5hAK5loySHNLmCTv9QuBbRSy3IE7wREFCbrD/ercj9nRsX+d\nOAo6mF3O4KMDuCSg6kXTOYnhvinNNKA307FkeGPwjMog7XKKuZE6CRxtF/MS\nzRgW4+HnzAIbEZ1pWlInJUlxn8unww8OO0geu2oercaxhduOAbQHcZYqWPZT\nTiRAJcHp7mkazoAHT0cCYqZm1p6cSxwflL3lH6m7FXDQo3Dxvzpj1kxRalJe\nf3vz7fRsY4hWtsxAVScoFUTg03Og39X/VuSNpWeDZHcwDtDnhhvWxabtbd2p\nvGYVyH1YBp2DXnASOqlJTKLcPmOv0VE1B+mU2mvbtySYCmWnwtVlJQUbU2xP\nbtxh55w1VZZHM228tHrYiKeW0Ck6nzVOgEhiLYUx+RTYZ9zBHxtvlzEZTDzo\nwCam\r\n=wg8X\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "e13ea32c8dd4774fd5e11325ebf59b43038c5bf4", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "9.10.0", "dependencies": {"rc": "^1.1.6", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_2.5.2_1523830900288_0.07639136131691315", "host": "s3://npm-registry-packages"}}, "2.5.3": {"name": "prebuild-install", "version": "2.5.3", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@2.5.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "9f65f242782d370296353710e9bc843490c19f69", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-2.5.3.tgz", "fileCount": 14, "integrity": "sha512-/rI36cN2g7vDQnKWN8Uzupi++KjyqS9iS+/fpwG4Ea8d0Pip0PQ5bshUNzVwt+/D2MRfhVAplYMMvWLqWrCF/g==", "signatures": [{"sig": "MEQCICwJoeRZsDEWi3eMfzXq952QQ+0PkU4vn14YbAzDl+lUAiA4mFRDdQ1Jr800u7IDqB7tCD43AZATkVgad/D5ATbBog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1KN5CRA9TVsSAnZWagAAt9oP/066tRCYDzf/JEO61O/Y\n9A8S6dC3bjzwVAzjJ47JTeJomwENS8Yz+1U6PtzMjIGYuaWSEe3kh2nqte+j\n4djOXzotun97m0T6zGtHcdc1eHXg2bcAGn4rRTcrws7CHbGuJYUQj3fg6wv1\nQKI+lzxOvr+uOd/XkJ88OHmCOv3nzw+5WpsRKbvhyEdLiQhxWhgDLPN8l06k\n4jUZx6+lm35nbckidvCrWVFvvvu3r32tST2NGsc/iL07nZ9n2KYBP3cMEQNH\nEts5tZScqO7z8cw17fmblqBSdwozIpr293ph3eh08BgRpVKYamP54AkUGQL9\ngjGBRVS896Vf9i0QPHTQ+I5TgvmWVTBowLTJD6Tm2j9lBloDSRm36qlP0vWM\nEztqOCsQeWGj/UfW0m9wY9G2tQqmskRjXROaUKsyKzPhMmKttcjgL3+O6VAy\n0LWoOMbx7oM05CITjw+o8u82RwtYk2YRTe7nPOvpjrsgP9AREYcOkcbiUsjC\n7kDzdw9B4GBkCTwwGvKKOGUd8A4WxFxQdPMrt80o74R7VVqnP2cuOyzcb9UU\nLGEivNFEdxSYgo+sLlw7ORAYwOmaXtIG2BjgYrm24WV9MbL+k+CHQdQ6m7HA\n5ywXHLLH6WPHz+zVbpphVDIi6FKeiOWc2lefh0Zf076L9EoQS5vIBYMml1vF\nY0Ou\r\n=XHV+\r\n-----END PGP SIGNATURE-----\r\n"}, "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"rc": "^1.1.6", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^2.3.0", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_2.5.3_1523884920725_0.3883338579427129", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "prebuild-install", "version": "3.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@3.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/prebuild-install", "bugs": {"url": "https://github.com/mafintosh/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "0fd5b2c48ffcf4ceefa64d04a4c5a328d79ad66c", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-3.0.0.tgz", "fileCount": 16, "integrity": "sha512-9kuOu84mRwlQV3JyD+tkYiJtTbeEVtPf0z91OvU89llsT8Y1mjZyz8jyMzMrCj+UynQzzaEc/p31IZBqXG77rA==", "signatures": [{"sig": "MEQCIG8vZmGysecTkTOXLSFFuuGAOrMMg/Q8by8bJ+dGON3cAiAuI3IMSVnqyodhUy/vzdiiMHwcTOHhjijenISJXcayBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5vOLCRA9TVsSAnZWagAAT8YQAKRsZZOlePtjo1QAW1gt\nEgGwgeTPvXFsCBU/3D2k6DzkRQCCLKSj+EqLC40dqTTsA5CrYpDbNMLJFYeh\nVgawanxG4H1mPDFfmkppof/M60qO2rPffOHA6mDSu0ItdPHvjC3sMXMX4OKo\nl13E5GdISnye8pOUgFezDn7kBF3TuNp8twErEHSVqYplUwe6lkPF8sXbEsso\np6lrzKwZxYq8EC1H5OVgUBeYR6R4GbkHMUhtksD4+WPmpUH3dm7tcBz4HLV9\nfuziJydI8eqdM3eqOtd1nDdd1LG6LhEdtnIBK1T6xzl4A3WrVYmsuIBw5012\nj2nKiI/P93R8i9kd/H9Wp+7TMMRG1fCzXoHGnvhrExjaye9tODnOTAqXjfMs\nXX+Sl1JEWdbo4fU8o6oujsW6VE7UFxnaItOj0VQLkKqi8A9YLyb4ITjDcntR\nACRSEqIQ51JWcPlR/CRglOgTB5JPuDyETZtpU+tRtYmQYuQLmjgnKT977/SE\nlaDEQrv0URDP66r0DGQ7nwpBzpwWG26MAym1IvyiB7xnJnZzSGeMX8Sbe4dm\n6qB9NjV+TzopOsBefHMTYFx8ZNxA6z6NDa3RmXf6Ilr2siNHtIeRuPtFRI8A\nueY0gvHXfxcTs6GzgIxiUKktx5s9Q9BAzqWI7YAL4A8OHFekfRAeyHxk9r7T\nrmYQ\r\n=O2Gq\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "00ca63231ccd38ec3b0af0c74651f95dc53ba173", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/mafintosh/prebuild-install.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "A command line tool for easily install prebuilds for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"rc": "^1.1.6", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^2.3.0", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_3.0.0_1525085065810_0.935378699389559", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "prebuild-install", "version": "4.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@4.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "206ce8106ce5efa4b6cf062fc8a0a7d93c17f3a8", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-4.0.0.tgz", "fileCount": 16, "integrity": "sha512-7tayxeYboJX0RbVzdnKyGl2vhQRWr6qfClEXDhOkXjuaOKCw2q8aiuFhONRYVsG/czia7KhpykIlI2S2VaPunA==", "signatures": [{"sig": "MEUCIDpA6x/H1rA3eZGBvDWOXrvBM4BgqfF4dGzbQqxe1LDfAiEAjxJXI9II0XZqCaM4DCOh6NbOfJNAuVgOpIqhGR8XL24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7YxfCRA9TVsSAnZWagAAbKkP/jC2Z6E/VHaZQseKsQVm\neUoLvoT1259vJ2nPX2+xiQukmUINZhab6AKbU0L4+Nzyec/LlVBVey6/Lq4t\n1JLhbk/ht/hfi0fXAtOVpYUZ0B3ENrh6UXvAEb5h2FCxLo1xbD3jn0lbtad4\nowUjt56SrhDA08y1GM2OIhrBHz4uGFAh/QiG5mqXXJWDK2eQRYB05kJrPDSg\nfVe9XPWPjvPucksYeS76W7lFrKxCXdFxlHCMRZjv1z61zYo6zYLMkjpP4y1G\nND8mlPKoO4TcyPFqJDL3SSt+9Uv8Lyo2fT4RjbayAI+ZFdT3PGihUQsM6JrR\n6y9V4jJdayF6GZpy+DpcrIS7qpLafyFeQlQvX05T16JthcQEl51FLwX+0p1+\nnhB2q3ARX2XGXCbahHlOk1xWUux1oA/UggoWbeVomSu1DChzscvSN+/jyk5Y\nUvY0omZ2zhVN3POtdLOlrfJ1ljwmOAReP+1JdEwS4WK8F4vXbSbR01Dqz7rj\nEebYwjslYCdvDTynP6XIX6ZnpdnZPE3DgzPt6T460z++o/kp2P5TRsvG8XDI\n6YQvomXNcGkGsLUfeYHrbEkkWkwFM3KYAbXtqdp87GxgNBiY9UoQxnkPJ1Jb\n6LZTbZunHTg3pY66wshF0OmisdK8oqo+OMEpouPbBM020vUIYy72jHAaXv/f\n/Hpl\r\n=tLSV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}, "gitHead": "062cb4153dd9b08e39d8ae34a66052340ea226e8", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"rc": "^1.1.6", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^2.3.0", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_4.0.0_1525517406108_0.28408013702994395", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "prebuild-install", "version": "5.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "bc1a24636beaf0af7945c0a84d4b9249bf191b6b", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.0.0.tgz", "fileCount": 16, "integrity": "sha512-AvcPLFqNz/hDd6o7qLj8i9xB479P9jSjA/p6m4927CRfY3tsmPfyFmD7RKXtdp6I2d1BAIVBgJoj5mxRJDZL4w==", "signatures": [{"sig": "MEUCIBjeqruYh4hBXpxHDyHaW8Hud62GgHIT1da9qvheYe8wAiEAlRaOIEz94A3z6yOLoUantP1138MZw23p2Vi+WUX2GKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPAHjCRA9TVsSAnZWagAAu7cQAIoX/1EHNnnVlb3kN2iS\n6VBEIQhhUfm/rD4jTiFBtq/WloeDecXnjv5g1RJ8cfM1EY2n9fy9YScQH/3B\nc9yBBOOcXhXH8N/uACgd9qIc5+E8I8u4e4yJE2gHLnGdH5ywgGs+XUETNoQP\n8nJztb58RB3zg9+V2NDk7e+m0DzyAI2o/vcrf02kTwIUK8VHGlIZn4NZai05\njD5EWszmgoNWgw3sq0jzvIUCaGOyrZRCWciRj5LrBc+P6HJwHdSVIKeNz4jJ\nRPFyvypujykRlJM8pSE4ZnTrM1DO0G2r0bOhY47uVnJ0fMuLRlK0RCOroQZx\nBz9kqII8IRDBcBuz9y3D/H/TUx6DfrXGyZ7IcdRH0H+LVsojqD4NvyMZLs+t\nMmWC8M0UJCojzCW46Xf99yneHXYIdzK4bQL1yqui9zNzeSYO2B/ZiPhXxHXa\nZYBQz4T5ghELPdbQWR04G88fAN8+xUwNBgLBos6U845RWal5eAwQ35kkaP3p\ny2CpWbnM5MI7BGJKGHJVciGnKQIdcuh1MF/1JuiaofttRb+ec2bVIAugSKjf\n+Pj6DKK3M08j78CUmg/ib88tDQ03+3qRUjI8JKE1IHndTiwVtgT5scZal55X\nqbg6pDrhy3ElqBdKUonrp6ND1XWqvh8k5u3ocJ8dGve0/1GS1LGlbuL5CgRC\n75ED\r\n=8luZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "17dd2d3e515261d2480f7bb6a4afe9974cf84f99", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.0.0_1530659299629_0.4676449785781809", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "prebuild-install", "version": "5.1.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "799cf2e443065eaff4880ecfa32b4d1a4a3ad000", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.1.0.tgz", "fileCount": 16, "integrity": "sha512-jGdh2Ws5OUCvBm+aQ/je7hgOBfLIFcgnF9DZ1PIEvht0JKfMwn3Gy0MPHL16JcAUI6tu7LX0D3VxmvMm1XZwAw==", "signatures": [{"sig": "MEUCIQDgKlU0ZYLLJB6XwRzKlz1VXHb7qJ7E/E0ISLq8Gol8LwIgNFR56f7C6kmnzdDF+otYi+XzxY2y9bGOqwUS29LVXNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhVngCRA9TVsSAnZWagAAx3YP+gLtcLvxYvu1fEv3uYSs\ngpT7oZJztHXfdMt0Mwdo1hc3jhEFoEbc2HN1em511jIoAcxx8gWmpCqd9+jX\n30ZOUjesDEb4GfKRjHAedrTrbdZYZkRRK6/faUXHJ0076yNpoKih2tHR6tBV\ndzutSHOMK7c68TwfuxcC3mvL+MvvF0tLHQR5sx69iT0uprSdKDqvd+HKYbQy\nqQ2xACDhaZHXPkNkeVIj23vfOYRV2SiB+H4MnH4xmMBpJjFDXYSr//ZOmpZl\nDs97ijY/KhzRdcwzeyH1AAaLVcfrZ7vvXbz+FnK2rGVue+NsCc2BvLxs0bur\nyyiX9vZxZmqxsHBrQ+JErciVKrhm68en3/UIcjVbIAUAfh6h3xrYhzFTxH+q\nAJRE+c1Uyb4WZh+TtMbmuMLVPeGLCyczuFjHrU1n0M9UYc6s7DicfkeilbKQ\nN16mL4bfJ2CAyZxt5QAIMnGLUyYquLb+NSEwXGnXFd2a4CEstawM+rb+fdjH\n85hZo7VNw8Fp6OkQUqdL1VeQtSh/haG7nUomHtOPFJODODwfyLu9F2IvFQw7\nDPCpWCqdH02xRU3NPSzBHtl06+27XxaOvSlPqZ+J5+WssmoqXAwZ/xDZCUt5\nZMSDr7dTenFP5x417L6ECRD0RndzuZvlRltrOzcn1INuLP8kRlEXSdIYf/2g\nEJG+\r\n=bQun\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "7384bf5e36370eafd9ef8ef885b8957f07456603", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.1.0_1535465949631_0.6426650623164663", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "prebuild-install", "version": "5.2.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "53c0422ebf28437047d699450bd4fd765b2951ea", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.0.tgz", "fileCount": 16, "integrity": "sha512-cpuyMS8y30Df0bnN+I8pdmpwtZbm8fj9cQADOhSH/qnS1exb80elZ707FTMohFBJax4NyWjJVSg0chRQXzHSvg==", "signatures": [{"sig": "MEUCIQDZlsGr2+etiyJJON4oKdeHweye9HEUAwwWc+nyKP7dMAIgauFvdRGaByoXFImEWrorY69HMJgLF/1RRKPkraNWQPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrSoKCRA9TVsSAnZWagAAZuMQAI+XWpAo2yErJQ0hIFlH\nm/XWhIpod1/eursgstf+//KVr8VewRwsHMY/Jp7NxnSpqj6757a4huECvbNy\nHZxnfxtWgOnm93GaeHM6sv9NC/xwOdTrY1DibH3aTDrXjSwuVXlHfM67eUqx\nGfdOKbUaWS9m5yal6gAS6iS2UKcle0/ZE61S5mmfvgeRbA3GA/5OxmNjz9rV\nUlXSa0dkCy7jZuYW5PWunyPASdeEsWlNkggjKD1x9yHej2Vm09yRJRTYtzdA\nxrVMbQUjgfEMb6eAJIwJvGml9wa30AsQ9SNJk0vTb4nyA2QomW1Qn99rpGw5\nxqzKsxBGi/7IoOzonsv1TjiiCZvn8YvPNxjGqMSLJUUZTu2Hkn//tO2YcDeD\nGs06rbYcKF0WQJzqksTW7ZotElejXKMwzYVygM/pZTq+IuVwChGpYePkBMQH\nCkKo9E+PZxSs/2++MAy7M06nPibqRtrS646tsD7NFurijtKZAZlxbOFsIBd+\njS3zcuCNNpa17vicsYuY1tdAE77zKLmuWiCCrc0JwBpu+Lu1Yf+AH+XvjmKQ\nkilAxBy+dSF3+R19ayijjWqsnrfUYSh2TlQLK12XgUdzl9vwWcdHVAtTPxE8\nvXATuZn4riRr8jLmYZqfM+V+oNuyS3sarC4bkDJS9avBGtYHnfuUf0AjEzsv\nW4Xr\r\n=Giqc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "cccad21a8a816f76c3f948383ef9e5845ce19e56", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.0_1538075146223_0.5338810421887914", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "prebuild-install", "version": "5.2.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "87ba8cf17c65360a75eefeb3519e87973bf9791d", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.1.tgz", "fileCount": 16, "integrity": "sha512-9DAccsInWHB48TBQi2eJkLPE049JuAI6FjIH0oIrij4bpDVEbX6JvlWRAcAAlUqBHhjgq0jNqA3m3bBXWm9v6w==", "signatures": [{"sig": "MEYCIQDw5rs7iRN/pg3bYhy+b39Mh82UynkOcTgKHmacnrVg6QIhAKpNEF3N/DzzfIe5X4LPInWnVmGJb2YEqtHTBddlten5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyLuECRA9TVsSAnZWagAAlyoP/31h5AhIiEBPOnXnlXl6\n5D6UwATOQQd53DXr5zLWcsCEF2brwiuH7tH29NyysTxYLjnE1NNEY7QFzlEA\n/RHOG+AiAvXjxfZAJh9AE05fsFMeH3J8UdVYdwCfrvuvcxbMud7WGCabvpFj\nUm0HTsEnhl9TXCZZNXsY670Sb1lFmG49M0/0xcvuZtyv2yfTuaw4H2GU6ED/\nwEETe/BexuGZra5+wE0N4McLKUJ18sVI+8zWIiBtT2XIWM0CBvMRwZtu0Qjq\nAAuqdsBO/hjuPsYFZryRlndGA9WtbcA69/uzO6w7DitgAeFRdobR3LpesCO4\n12SJhK2j2FBDBsJiIKbpBy1jxtJQ+ePqtzOa9DFeuUU7UYrSYb6jpSSNhzrG\niBttYNLiJ8A6xVKKeArzIWyQiRlvFNuagAhMoAh0hNd99ZF1rSvSqeWJjPLM\nr1JDcuTRPnuWkpJkENQpFJkjGNEJeLdBfHnWguvzlJR0bmoYbgdQlx9pW31D\nThlSIN/Jso7BATiVhw1d3YO20+vql2TRy2LXyKxgkWUYL8viivT/gN14PtfR\n68aJZD+vykpVUuQs1LkRy69iHlCqBGHE5Kx7NcVyAavid4Xe9DT9YbPeWTpR\nZ6frKRYJa6Wjkp5TUK8ZUkdaSMJeV8uS33E/KpvNt7jjPs8eCqQduOGANPXD\npi+z\r\n=D/qj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "0da7f4e7d838dfcf4f197350a3de960ee66b20fa", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^1.0.2", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.1_1539881859328_0.5110587660633801", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "prebuild-install", "version": "5.2.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "237888f21bfda441d0ee5f5612484390bccd4046", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.2.tgz", "fileCount": 16, "integrity": "sha512-4e8VJnP3zJdZv/uP0eNWmr2r9urp4NECw7Mt1OSAi3rcLrbBRxGiAkfUFtre2MhQ5wfREAjRV+K1gubvs/GPsA==", "signatures": [{"sig": "MEUCIFVZrOY7l5IFb5ncmHfJEVI4zJ2PSF/Czmb4jNY298jWAiEArwGEGsKwA5SpHEcxZKh2i64SBvz0u4OlXKcDiXWx7J8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/yVHCRA9TVsSAnZWagAACO8QAIemGzdeD1pbl74QrWlg\nQC6mr5GeCqucszuCp6apGinxgLG0T3PcZBKZA4A5wLIOIYMrxmmSgOzxfLZo\n70kNlXHQj7Zp4nJc0I5NLSUB7ovdDeZYmM9l6nFCsRVrFsam1LmqhXIPmsKr\neOw0+qeKFRfGYgoXxTHGKdNzcddDQWdjW1OOV3itWW6XHdMFZ2ZpTu04jA2o\nLPhwTmBformcITTV6z9nCY/vZQRyTuhktTEV9Bg2MGFfsje3Z43qEh0Dcjz5\nmNKpBOc2IIzJPhlFblAE02Zq13xjuEMq54kFynNdYX6qo2ywPNZttNU3oF3D\nI11nmqkrN6XcBpPVmm4zfv2mb6s9qtQeFa1pBbkLRgxVxWBl9ZH3wZnHpicR\nIobGyId6Y0yUgChEVUqUof4YaRKEli+bFTzDEVntK1GCo3zPsiCfAupFBY8W\nmEzu7M3Ojv+0x4pGvxPJjHkiQa9K05Dg/dXNg9Xe6Sl08ZRc8S+kIrnHw0gE\nqKtzvKQeetccN4L5GrZs9aNPKVBtSJRhelrRgQDPrDWQLfaeJg29OGanOJuc\nGhgCqIUjMZ1a4B00m/j8czHa0ylpkaGlDd8WMobyZ7fOQJBaXUM6m5g6LoGq\nkSYJSf1atlIc7aiC2+ezuOkEIwcAykNrNY6oYGpkUK/PnAzvczcQlx4N8u8c\nqm+5\r\n=s5fT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "ab8eb4c83d0fc72904cd6bbccb8a8f70e8fecdf3", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run audit && npm run lint", "audit": "nsp check"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "11.3.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nsp": "^3.2.1", "nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.2_1543447879138_0.24225429868291837", "host": "s3://npm-registry-packages"}}, "5.2.3": {"name": "prebuild-install", "version": "5.2.3", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "096f5116e63eeef57ae533a2ee460b13d7872704", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.3.tgz", "fileCount": 16, "integrity": "sha512-sdijGV8EkK244XbYJLRhOLiHj9QZKUaYW6uDotEPpRGrC5FUXFr6mZhGHr9NjhU0MISI+JSqZ0geVZMs2kIDRQ==", "signatures": [{"sig": "MEYCIQCGcMS6o8LivaNGPGU04g/ttVLuBEiSAyYDee6JuBIxIQIhANTsZNY26MgiA4uUldw6sT3G8pAl6L8KExblxYisLn+S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcU1wZCRA9TVsSAnZWagAABtEQAJWASNyyTITlpyrFbqO2\npFYVmn8WlNH3ZlqKXyPOdekj0NtNHOIN4NfaDyLe5Lkemo8QKMcZ37d8rvvB\n6nDFRNl6rDpN1Di79c5klar7Uj2L77aUrt85LMl8+d4TQTMeqlAfq+Xey5jU\nywtebXnzjgUjmW2TBnuUrx8+tmWRMCjGsORfCodeXo48cKx48E4xPDWKDW17\nM+EEbi1yZDtQHzCR3fpPTx/8vCKS8sTy2mFQ+sSv7v6NytczZiYjCfCkSjW6\nVEOp/5tLxvGJsMnHPaNiuA8cZJn5FwzoHHH9rB4BuE3xxam6xgHDEjvJEvcF\n9UOsKvbXT5ge0BCC/4Hy8lh1th7ExABMhSVj9uk47ikdI1zpFsHvGT3Jw0wi\nai5rBipGAQbhqMqKae1Pxx/u2GISFZKtBYuNp+MuEL1UDryKkdxQp+/f1XwH\nTVSs8ipyyMPONjuGWle6RaCZGKCTDNk5zdBVCmLHACDTJ4q39oGlvCfZN1If\nXNYRhtnYNMhmR4JBJtDecMT+QuCsAeFoJdL89n2srWvAOf2KzgC3zzZ036zZ\nqeHw+JSaqq8tvR343zX2HSMtH5+duimVKksQkEVIjY8VTnJPp9I/siIqQwAJ\nZKXR098Ne6bmS8vVTXW0PY9xgQAtZRjAP834O9fA1lihr/KMKm6M3lYff29D\n1EsQ\r\n=xxC/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "099c704fc6654637edf3a1ab4e85887fb150a5d7", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.2.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.3_1548966936788_0.7057701869518755", "host": "s3://npm-registry-packages"}}, "5.2.4": {"name": "prebuild-install", "version": "5.2.4", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "8cc41a217ef778a31d3a876fe6668d05406db750", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.4.tgz", "fileCount": 16, "integrity": "sha512-CG3JnpTZXdmr92GW4zbcba4jkDha6uHraJ7hW4Fn8j0mExxwOKK20hqho8ZuBDCKYCHYIkFM1P2jhtG+KpP4fg==", "signatures": [{"sig": "MEUCIDM/INzwOHVWGbmcJUEeaNII0TkNbbmea2CcvIVqrGrAAiEAj8GYM7INeoh2l0m1dAB/zWbSkhkVz269DomJliE6Mf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWuslCRA9TVsSAnZWagAA4BIQAIWQRyqsqr2wOWL9yZF0\nrwyz3Rrv29KUYeDJ2b+Q4wOhbpIOCeSI8PLygOXIQ4egdY/VP5rQQxO8m5xX\n0wWa6oqto2BLGix2X2ukkQ+cnXGnhxRr7aVpnTihoTJNr1XBIqbXcfZGp9mo\nfu56NabcTP5IAjTMm6NEoB6l29C5LD9b+cirkle8sxJifnyQC55DEMtYjOWr\nYnXEwPuwRdMMiRx/Ln52CAuF97nShy2EcW0S2rRLm1SbYLU1UT491CVx9ERx\nZSOiwiyXFUl5+da+Y9Eo/aywqJql3I5TQ6HdpfVX2AvpX+gA+dUDzkTkbPCE\n2mNS+dJIt3MT4brjzhUtffIRPmo2OgrgzNKOgTS6vSu//LMroWZhxN/auooV\nOTt9ECMQCjxCGU/YroC7LdHuXdIGAUOxRu6Rj64EEOf+SP2rpEqySC+Uh2xg\n17CxNu9Z40bCxDItutfdstXj3pRdflJk3BH9lHiOrQD7qDTAH18Bv8dEuxcb\nDi3fLsxBHMhLK8Mq2jTRtJUkaD0FTYLPk7T/06njtsMoXQ1daYTPAPHaXi4J\nGTGxQfqAC154rbj+wI7J+dN/UbpWxHKAOLbkRypBAQciG3k6EIJZPhziqd5N\ng68m5tAFpuB5EadxghdwMITB6GZ95Fn/Hxt+UeVdq6vGi08cbhPon4kJV+oX\nnNya\r\n=WNVb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "2a6ba425eaea6c84ac768a2ebeaccfc9757a3b77", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.4_1549462308627_0.5572059113820502", "host": "s3://npm-registry-packages"}}, "5.2.5": {"name": "prebuild-install", "version": "5.2.5", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.2.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "c7485911fe98950b7f7cd15bb9daee11b875cc44", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.2.5.tgz", "fileCount": 16, "integrity": "sha512-6uZgMVg7yDfqlP5CPurVhtq3hUKBFNufiar4J5hZrlHTo59DDBEtyxw01xCdFss9j0Zb9+qzFVf/s4niayba3w==", "signatures": [{"sig": "MEUCIQD0fCU6UahLWTvsRgRLnEt66QIY+aizp6iMK09ZslQB8wIgdmRpduEyP0RRZj8ECh9Knd0E/9uGjcEgQXslch8k6jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchsFeCRA9TVsSAnZWagAADMsP/3OElXEXL90v7eKE/Xvu\ngtznWjROIz/n2HPOGYVW5UXQrlMF+jAnZzT5FzvJlbQYGeiyBboJHCHP3jjA\nIjDXc2DGHP7nRLcKfb+PPTArbN1q9giteAHBPSSZu4PTevdxuZZirdfE0Yzw\nhByvB7ghRK5U30KkVu5aV3B+GrabaHqtBOQoHhNhQUwsBJa/LCdYUJDAMMFo\nK4xADxFQNp4N2HhUWmLI406zxPM1ZFWsDRTh8MF+5PL0iJC0hMfkAvrglGzh\n40K1PwhU82xpUM8qI9pfxE95XqGvAsf+9pbA1iLQVWDJlsrm0yg/zhGagInb\nlE742GQqHp/nkE5OupMW8YGEeBDR2GEmL4fQ1Qnoo1ZdDQDhARjneVqE/PD5\nt/SYKo9kKUKqqQUvhna83DjGvRtnfKMjp6FVaeMf8XCmvUgQC5INGL9Y0uqw\n1dvBGLBKkq9Pmk9jQgm40y8OHCfjZ6ORWQJDVo3wny19btjkRgWLpjxMzmwt\n/UsYaeDqJMMBqNpwoT+GxLUyqbxkC9XpGiIpsCgdok7BxdNjUG/kSQAEjwi1\ncpw7Ps5L+1bv1VOqAzxTO3IjwRGWjrcravJ47eeUfHvAo0/W+MDvfcV8ijuA\nhpk/lm38zEAn2gppvOD/X2zOtNAl8/NElZFTLddP7H69yNAw/HkF3UPT4ghV\ndJE0\r\n=13w1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "c2697038726fac585f6c4858a84ab2ac9901ae99", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.2.5_1552335198169_0.25859715378941006", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "prebuild-install", "version": "5.3.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "58b4d8344e03590990931ee088dd5401b03004c8", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.0.tgz", "fileCount": 16, "integrity": "sha512-aaLVANlj4HgZweKttFNUVNRxDukytuIuxeK2boIMHjagNJCiVKWFsKF4tCE3ql3GbrD2tExPQ7/pwtEJcHNZeg==", "signatures": [{"sig": "MEUCIQD2A+In+9n9y2j0HxSx4uGUPLu7nrVTBPTa3ojgJRfi7AIgToUnheZSDtzw90qVVnoZZ+GHQztSW5nvtASRu27K5wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvEDgCRA9TVsSAnZWagAA3isP/0dJDHI7bBHrMP0AN9YR\nvTleE+Qn3XxW1xoMmNMnW+93AvjC7RekFPYj4nTOW3PgykFStDNMmNjz6aba\n9VUY9AwfJ8W1D0DL/GfA2xHUMVAGMTa/EhcnhJ3Rd36lBkBtq4pBv8wb4wH0\n8XZWf8z0o1GxwX6mOZzAkzqYmggCf2u4o3lQ5m8ixuUlbst9ntg5slrsJ9kC\n6xGQNYlz8vSUKBeja5HamNJ3hQlNzjLN2gxnYO4fjWxWI+l4dJx0/UK0K4eB\nv+Q9/xsRWcnOJFRal8q3o8qQPkGBtw+Dw1DOLAUuYiepkAZaTSC7yIapjTcB\nipGnyaL5sAhCY3afVolN0Va3qOGYIUpx5PMC4SpwrQeFEe83j78zmmwFAxA7\nPyc59Og4a7DV7s6uHpyIidRxdDZJU49s3sd1JY1+xHmswT+5zRbxcjTcqqzY\n93rOukhbDsTcJQAp2KUslNAcrKBr1kZVXfwPvybasu863s1F8KO3JSgtn/IF\nSPUWTBkx4quMk/3XyOBr3KhXTBEw6o/4M1GlVXu1/OL5D8ZiUkczw8EnwAuP\nrEmoQtH8ZNsv5taHIbYkPm0x/WVo7Fq2nsdfYLmKXyWi6bUDo7CZrhpOs7i0\no/ycKkRl2ugWLNliy7HY4v+f+PILAn1ADc+UCBY5g4O0hA1x16S4DX2girBA\nVhpN\r\n=vD1m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "bb3eac6c6f0dc432326e1aa15c7c9f5157ae7fb0", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"rc": "^1.2.7", "pump": "^2.0.1", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^1.13.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "os-homedir": "^1.0.1", "simple-get": "^2.7.0", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^9.2.5", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^10.0.3", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.0_1555841247915_0.040151001473928805", "host": "s3://npm-registry-packages"}}, "5.3.1": {"name": "prebuild-install", "version": "5.3.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "7d220b978e03b0e067e0d8cffd13e84b3515131d", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.1.tgz", "fileCount": 16, "integrity": "sha512-lRLBU0JPXBbpC/ER9PtVYYk1y9Rme1WiMA3WKEQ4v78A5kTsqQtrEyYlbghvXCA6Uhr/769SkhibQznjDBRZpg==", "signatures": [{"sig": "MEYCIQC2bumq4G/goGN8pXIppkpgaijULpY5KBxmakX6c89mhQIhAKHWMd81VL5DWvHQgIJWxqisxI/DmgJSFFJ159E0+DH6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcpLaCRA9TVsSAnZWagAAbAoP/2wTR5JVvcZCzNiVU11H\niv4GN+6yXaFdvzt8pBoxS4Ooqz8H8WOxG6sVrF0p2E+dGRNQH+cv3f4f2upu\nqj1++Vj2m+jXTK3oF8Ap9KxnRjvGzciLQL8OoKr7C5I2xdT8OCLXNIz0jSRi\nUWCfG35+YGjbX5F/dsgTX+3PgA48z7/YsKcTLqJ2TyKEoyZLTjzh6zijsNI9\n+bxNqyg5wkSoXpra1KqTk5lLauUyq5n70H8OwVHHNz/tT6nmo11CtrNKmCEh\nKbpa9ypdhLhMOwAExKVendlrGBG25ryRZScBj2EGwueOkmTjEWUgGIeDRnDQ\nAMWx2hSV05i7vKJQufGQQXr/G/Z1XLNsye7vJsrKv2DDVUACREZutZxP5x8k\nj45duiAJ/OO0pagbMWGASKa/mVJKGOd9vy0A5u70pcXA4yoNN18535A+8clt\nVGpvM6YRCEJgiT3zF46Axvf/SZd5BRXH8H5sayD8aIFv06wPDAE32OIA3E05\n39ygoF6h6Y2ZHNZ9O81h8sUeH+DiC1zL2v6hKyNrdJkBPcN4G1iBJsZSFF3S\nXOY+dFY8rSljPo7aYOq154joTwAyt8RsA4tGPOq1sCKO6oPsq5sEm7aTqsti\n8RUqeZ9cIodPuvHrj6FgVCH2UZz7i6vCedrYRd8J7AoDzF2fqoNpuAe6LFrS\nPvQU\r\n=euab\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "652f16941115d7b9aa1a0d0a27d85da21e11789f", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^13.0.2", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.1_1567789785784_0.18121663528167709", "host": "s3://npm-registry-packages"}}, "5.3.2": {"name": "prebuild-install", "version": "5.3.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6392e9541ac0b879ef0f22b3d65037417eb2035e", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.2.tgz", "fileCount": 16, "integrity": "sha512-INDfXzTPnhT+WYQemqnAXlP7SvfiFMopMozSgXCZ+RDLb279gKfIuLk4o7PgEawLp3WrMgIYGBpkxpraROHsSA==", "signatures": [{"sig": "MEUCIQCmw4xMHeljLSJkhXieun1n/7RlyfW6kvLlLnd1P0eUfQIgH2dvXlsHObTPlt/NwwWMPYdrMV+7+acM5vqLgMJK4XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23855, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdelB4CRA9TVsSAnZWagAAQp8P/2acoSdyOjlM+9HuP7Ns\nC0DMFrinXKHTZzkQ1xXfQS+EsUn/mWba2gXbEojcQkxw73OUG+dB7roOSO4m\nNOvfglNj38lIBr6NnBVhmDeOKvWPC7mJ6+mdZp9aynT0WH3lrJGbg9FTTf3N\nPWKCR9DFuPrdgNISAKy78baqJ6M91ZwHH1yz8Zcuc8SJSkCbG8ffSe7Sx3oP\nS87oyDiggEq56hZpuhRNQj/CIit5KAJaQiR4k/HFtj2wU0ALY6+CHnaEFOE5\njtk32N7JlTEz2mqzzSiEC71eWULQVsd0JGGYuKFTylxwLa7jsijpIcB1Np31\nwpMhV/KL7n0qYzLIkZISKzqwCxdHn//7zTukHDstpEyC2cvTDmQd+keN7hRv\n+QUeVtYNOSveB1vTk5veCW9J0jMGQQudO/vnTNVglGL8EE9Q2f5H4aeVqrId\nAaNONpmZnfSr6k4I/0WaxXj8K9aqvFvSxHZDbjjWzEQ99xJuyl/odg7AsQ1L\nRnYe2tEvES8QP1nErsOlbrXb+0nHYyqIlvYkOkYJMgJLRP86ZJXr2md6B0OW\ngY8c+FuB/RwLQm0I/Tetan5PI3Pv0hYkaoJKFhFPX4D7KDZ1QDF7Rv513cU5\noDs3MeHiq9QVsBO5vHW2m+9tifYqdJdnueCVLBxfqi+LFd5uiJOHnwFKLg/f\ncmRU\r\n=CTib\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "a5ff6d3899af47428ef19c8212d0d49a0c4bb447", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.14.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^13.0.2", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.2_1568297079344_0.5069270002891808", "host": "s3://npm-registry-packages"}}, "5.3.3": {"name": "prebuild-install", "version": "5.3.3", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "ef4052baac60d465f5ba6bf003c9c1de79b9da8e", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.3.tgz", "fileCount": 16, "integrity": "sha512-GV+nsUXuPW2p8Zy7SarF/2W/oiK8bFQgJcncoJ0d7kRpekEA0ftChjfEaF9/Y+QJEc/wFR7RAEa8lYByuUIe2g==", "signatures": [{"sig": "MEQCIFYI5VP/x572vHuHQApaLxa6YfyqIDXjIjZUHSIxuaS6AiAfLvoIVE3q/748wRjIeHEP/ZBcF44MXDJSSTbqSWlLdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdv1EyCRA9TVsSAnZWagAAxEEP/1r/+VgSa3iBBQSoTyOY\n5F15IbVx39li6niRzfF18ucD7HI86z6c2VV8rxb6Bg3gSiLSmWgCu0prLrkM\nTypb60BHOGGOV/MLFXxllN2GkhJfh+ItYI6dYwLWUqkAKnjYO6h8BmyyHE9I\nfaNaHRjHma7DniHYy7zSZmh1jnqo8bb1SBxyhb95LSNXffQwm3KjX8SIwDDZ\n3ACM+iRgnD6aDoIgINY9BFc0QTo9ISQkNdLsd+y5ETPKMmnH/sEDzuYzKiLG\nOvZwqRyYGGpdii6bmLXxFd4gUvfPJSAzaH9a0aaeN4X7FMl4nctLwalVWwPK\nkNjUgfDk4A8/LxAySzL8lE6pM0GXWptEkyOlCnk3Aux+No+R9yoFdtCexyfq\n/ds8MRB0+X0CEF6UzmzPwWz8cjOsIr3T4bqLDY81Mxj2qp65ccAf8IRu6hkY\nSCT3EsGqtFDZK5XUJZaUcTvl0GLCVJyOS+knXTebw4wK7Gs5gMrfwA/BLT0E\nrEhtUDkl3/T/n1RInUHemzXA5LwREpVkH49WWxqlpDkuO3O4PlzoWJI0Cqzr\nG+/sgwpbZsSJ/o1T2yqL7oyJxhnNGN5jlWoisShtt3eEW53gze996qaulJuC\nsCuRl0W8vd79hbmdGPIQMidVHtMhxttN1d13W1coeV+9m1vfyphO/uoAhpus\ndotF\r\n=ibF8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "84e610b433b2f3ee486761e27139fa3f368135b4", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "10.16.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.0", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^13.0.2", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.3_1572819250370_0.40267131730012795", "host": "s3://npm-registry-packages"}}, "5.3.4": {"name": "prebuild-install", "version": "5.3.4", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6982d10084269d364c1856550b7d090ea31fa293", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.4.tgz", "fileCount": 16, "integrity": "sha512-AkK<PERSON>+pf4fSEihjapLEEj8n85YIw/tN6BQqkhzbDc0RvEZGdkpJBGMUYx66AAMcPG2KzmPQS7Cm16an4HVBRRMA==", "signatures": [{"sig": "MEYCIQC3+IPAMMPBQYpgLLKFgWJ0KI59rR6+q26ndTNLv/5ufgIhANnHFy07ZEAO0AWhX64M/7Aw5z6JH5VGoJQotCR6xXFC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex/8hCRA9TVsSAnZWagAAvigP/12n8/qwX4p9Ahfk4pTK\ns3x4MK+dvlak4lMuVSE+6IdYJww1TL56yJ31fChOe22Z/baEGomaJNAqmWgm\nSrWPIbpicv/5TK0X6kyASpCMhG3zCkgyPBiNNC22TbK5zEbYxnI0RrV2hjYf\nB1OHHUQtctmrO47SKRE/TMMjnjPgQ+4XH+XDU0wVAvr6Rjgzv1ko48q6YoLu\nc7FMMttqg/U7RKs/SNrIN+bV17lDMxzYqZJzdAfEiR+n8CmryUkI8Niwuj0N\nvKXyaOrKqLK9QYo7TPV+dIezA9NucHhC3RuA8Nv38rkIT7D1dGwPm1gt1FM+\n8iJL8pqB2Yaogx/6vXufrgLkwoMg7iWhiNPRShS6SlqGCEGtrwIVmlGR89Hw\nL3AyQ+JjB4MnfDGBQk8HE2md5bChsH+8Ic+ijvl2ND9pwMNUFySt1Hqwy9Z2\naFUoZkVWvAlVhwX4yWe0I+3x+f5RXsi7TjUs12vlF/2E8svUD06E3eNptlkX\n1xC1eJ2PpV/G0zFbB46CI6ejNULCiaAx68MBkl6i3/nlKf5kCRa6p1sfz6+c\nmvAAcFwTKfh60GyfWokP1N0cGUOcvDHWNduA3WB6iBbdstUqqNPZAeLpqQZv\nXlTytLEn2nSQmyv8p2kpTJTKt9tHVAs+HH8d7GHZff8EnOrwkfj5v35t9n5J\na5tq\r\n=Q4a/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "5deb710376f9e3462662c9c8be083584e8d35903", "scripts": {"lint": "standard", "test": "verify-travis-appveyor && tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^13.0.2", "a-native-module": "^1.0.0", "verify-travis-appveyor": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.4_1590165280653_0.20062981478668074", "host": "s3://npm-registry-packages"}}, "5.3.5": {"name": "prebuild-install", "version": "5.3.5", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "vweevers", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "e7e71e425298785ea9d22d4f958dbaccf8bb0e1b", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.5.tgz", "fileCount": 16, "integrity": "sha512-YmMO7dph9CYKi5IR/BzjOJlRzpxGGVo1EsLSUZ0mt/Mq0HWZIHOKHHcHdT69yG54C9m6i45GpItwRHpk0Py7Uw==", "signatures": [{"sig": "MEUCIDrETe1sDyYV0FaUijM0gRG+8MHG5mYCrsmGMNkP6wuvAiEAq3H9tZc38rp58RjOOguoz2qdW/2lEZe6BhuWPIZwEMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9ZDcCRA9TVsSAnZWagAATPoP/Rzrstc+hMRiuTTnTsQv\nWDGOqqv4uH+AXX6CVQYEQ2OwoaitimWbk2H+6WbG7E5NtLLijw5x72bpy4Wn\nd7MqslZ2RHxveSLVR/FGVC5WwdWhQ+/4Nr2I0BgAxOHtc5Z3sgBVe3K+rGKF\nvjowr3EQgLdHbi5gNhNQyRYBhN3D/SIxCrQjMDSVs5AaHjb3D+a0n5TaT76p\nyPW9YtgeQP19WqG6CwEElS+32rhEcaK1i51vvPh5PblC/c/RNF+zMFZKTmH8\njguGHiWSl60ovLsLjryTP10jeqUUedQKhZkipZzmgptYPqbK9BhmeCqnCJ1u\nnQzvlhkIj2ulkim5TVdQn3RXU+dIDvs20riFEQ5LvlnKVj57q0YzXZ0GinGF\ns8izIwEKURBrS4Kwz4R/QIUH5+o1a73D3NRXYnaMZTU6C5NZ2yvEmryBAden\nM1JhHdbbr6LJrCpJwqzVh0wRnOV8SMCpdbeHu45TW9iPf7+DhJ8lh/1idvVf\n2A4KlyT5vafmlM7hyhixy8O+8UIcjxi/cfzjUUARqcDcuL8xN4ar7gs2+8ZL\nHyK5sPZinRG/GVcM1pMQARg4BcAaWrguxpxHUvCRClS5Ikbbd62paZe/xO+z\nWsJoUsdKfmU9DSkPIdilYzs5AuhzjkTkhmA3Y2SXfGU7c2pxyZPjP6c87YI1\nii5B\r\n=hqXD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "406b0513833be7c9e909128e642a42e82242f720", "scripts": {"lint": "standard", "test": "tape test/*-test.js && npm run lint"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "mkdirp": "^0.5.1", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.5_1593151707936_0.9411240898139199", "host": "s3://npm-registry-packages"}}, "5.3.6": {"name": "prebuild-install", "version": "5.3.6", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@5.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "7c225568d864c71d89d07f8796042733a3f54291", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-5.3.6.tgz", "fileCount": 17, "integrity": "sha512-s8Aai8++QQGi4sSbs/M1Qku62PFK49Jm1CbgXklGz4nmHveDq0wzJkg7Na5QbnO1uNH8K7iqx2EQ/mV0MZEmOg==", "signatures": [{"sig": "MEQCIGcNjXFfvg07zJMetbQkvVbfDQe8U6HFn2bLJy7eHtDyAiAgpw3EcCDZYDy+B+RgvCUfAmra6wTDtGVbcgBUFDfWtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjrqlCRA9TVsSAnZWagAA5IAP/1PLzhM6fZP/X8w1tuwo\niiq68/jIT8oaOt5ve1imov0UXIzwSP+0GRMjcaGjFNTxab7EmJtAieRyL0VL\ngz2wVnOgbMWaXFW9MlcbwQnqmcMJka6t/gHUIA0u5ygA8baUpXWBZSsNZBZQ\nhRKwWxh1yoqyv9VWouPT1lzkNKwqtgIXtZbR8c0hVSbC1feSpxtyIXu/Ocfh\n2qQuv+aPmxs/SqiPFn8pjuPX6p2WBz/9Cpp6Ml1ERv+hIz1YGoQZnbIykrWF\nIEXIoHZ6w4krnAGEkf9ROMKuXntlrfP9MaFaSoJdRh+EKI4FeEUygULlEydr\nIOMQrjuAkQ5O50q4Evw1r8Delfww8mivav7ERCOAOZtfuzyN1/Jxjwrm//In\nJsPAFpzffCfHmvgvzEXEBk1PTwi2a66n1Kn0thElJlxhF800T0Ke9EHQRTuE\n1afiK7R4nqkdx4abP4yZIHcq8lZM1US+8YWnsA5BXByiJEaxellmBdY0syfc\np86obym3ofAO+oiYKrVFhD1GYCuyuhkltgIMc4bZZoELSChZ4k1LI63/zH4m\n9/XG04u+qaNE+1Eqz2TqnUDq3cnjSk5kEZnJjykUvCAbONHKeZ6Faxh7zxK8\nznVQIddgC8uji/Oecm829bzxVT3VNhxH+2F5uUs1WUL+nq9IIoSTa4JD1ahH\n3I4B\r\n=3h9F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "db6ebe7d1a1d0f12cbc5bef54cc3d105c56a7946", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_5.3.6_1603189413057_0.23540999423187192", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "prebuild-install", "version": "6.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "669022bcde57c710a869e39c5ca6bf9cd207f316", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.0.0.tgz", "fileCount": 17, "integrity": "sha512-h2ZJ1PXHKWZpp1caLw0oX9sagVpL2YTk+ZwInQbQ3QqNd4J03O6MpFNmMTJlkfgPENWqe5kP0WjQLqz5OjLfsw==", "signatures": [{"sig": "MEQCIDTaJ4Wz82DTYuY1PredNme7N5OrWnFyqjkvZga8lwd0AiARDZSMEhfIoojCJnrshcgsAUvynVVLOZ1oP5FYzhZH9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkxICCRA9TVsSAnZWagAAL7MP/1QnjYix7uD1k+HjOhDv\na4UPbb7zGjMoJed99UJwwzq+olXfmXRGxYtiieM6J4OjOZa4fSF7lYiC4nuC\nSgBAFXMbuS4lmo5Tb2SNlQLUTdAnIC9Ezq3KYq8wQxZL9NvruFACAgnqXIps\nRLuZhR3Tnvikyo0tX+thGuY00/Yf/8voB4coHYJTANpDXOhiphXbmqqmaXWj\nV9wsCTB7e0ATiyhaPsT4Vom3Gp6ffpZL/0emQsNPDuHZnV+9IKtav70jlioe\nxRMW2yQIUZWNitRzKGCdkbY0leo8AGDvoxkSjdZJRl6vkwm9ugWE7Yu974Xg\nK4jalwTCXUjJBSxAC119oGg/oH56TqfNQ7hAyi+uf1NZvIck11/QSItGXTc/\n+19gTXpxBg29tnW2a9eViFxE/PHCtKLlKD0kElkhlfuZ5ib4Pj8c5ZfRrntw\n2G31LUJDSgXOLrZrZST9g/NFBqc4jkIvPsl9M3ZDorqlzgEdb/TfS7uXQtdy\ngDHXLTRIx3KdL1t8B+JUj2OnLPDMSE4Y3dwACFOtxI/UL8URAx9HtJjqsipQ\nW/pj2AA0+3vtZrxwgCyaee9JA+yCJHsTfU5uvzEGrEe5pnlcgG39zttnecOn\nMFoHqDlL93XCPhkoszmkkjstpJewqmYxH/ftpYOTuU45zalmdbpl+E05vS1g\n3KxT\r\n=uEeC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "9e62565f5847d5fad887112969c13df57e96f0a8", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.0.0_1603473922076_0.6965822790564313", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "prebuild-install", "version": "6.0.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "5902172f7a40eb67305b96c2a695db32636ee26d", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.0.1.tgz", "fileCount": 17, "integrity": "sha512-7GOJrLuow8yeiyv75rmvZyeMGzl8mdEX5gY69d6a6bHWmiPevwqFw+tQavhK0EYMaSg3/KD24cWqeQv1EWsqDQ==", "signatures": [{"sig": "MEUCIA2n4FSBumieIIuFAXXxIOaR1A4+9GZtkG2PH3A2+QlRAiEA59/7mvN3WUNCASyLKNKAhXMnhmcenULTrlki0OlWwHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKRlzCRA9TVsSAnZWagAA6gkQAILFe4xmjfSh78T+WkeM\nGd4exxTdzhrPvgbsNBbZEfOtONBu3huKPuGfsiFTjoYfTTDJJ74M/n5OJOMO\nnGbDAWjwbdhSY5Kwx9Nk7t4GD8d56yO3zWytH/7j0VFcqBx6tX5wCzUci1Y5\neP4opilLWbVVf3FTzdZHZOxZkpBL8iR8CztCXR+IYsKdr205pTqzT4k2qnZw\nqisyqvX5YWWGWm2Y2R6sdwby1UhTFKBhERdoKuY+Pr/7JmRQC+f+AIOmdflG\nLLGxkhB+RF4Biq7sw2yXoB0i+rTT0otlIiSCiOnA2EqcDvHeJMWwf2cyuZQ4\n6+qn2zR3S5z4e3bk0CPzwrsYFJK+TPHH1Fp5pNMmLy1XreTU8eUjUUvmO9Wm\nLBeefclJIbithlp5g+1U4Zv7cqvW7QYemoBcqiutbXGD75NILVh+StNZjZB2\n4s1VBRjt/RdQNI8tYlPgbHfmrfpEqOz0B/gqyuLJonUtoeXH2F1+dsEOGUC3\n6B23deeorFxWluHVG3woux7iju2AQEvU9nDkveo/VkF07xGja4hEMUcFXZLi\nqGcr702ppyjBZGwQpAp6YhZQ28wRQtstMMzs1TODG4uon+P+UbUuzt+nkSZu\nvTAKf7lU83kiZ9cGB9l5loZEjCXex7If1GwQOyLb4kazzEr57+G5j4eAUl3Z\nuRZE\r\n=8pEz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "43d581a7225d1e922715b1d1e84ef98ffbcd1033", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.0.1_1613306227465_0.8329700839342549", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "prebuild-install", "version": "6.1.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "cee5bfb0317475450d05dbec821be23d9a7c2c32", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.1.0.tgz", "fileCount": 17, "integrity": "sha512-ht3ts5ewK1V0RqqelVVBcZh94WEtk/WCWlIzeuztMLdNe+5/65jPGr3sgQKY6tJhX3ib7glckPYazjkW/K0D/g==", "signatures": [{"sig": "MEQCICUtTds0lLFOjOP/ItZB3p8IQV2i3s/G1wzejsUkjSCBAiBEV5obEq+OS19jH8d38AeKpe3IDT82IKIPMxO7Y7wAxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgaDi6CRA9TVsSAnZWagAA8jkP/1vhJQij1saEz4TxQjpZ\njuJLU9o3+olpIy3oBa2zI58E0R8T5pbjDNL2PQCm2W5ALDwP95hj0SLeBe0X\nb7FW7eIK2z6KONSYTtKN/jgKGi/hKTaXYzsiWmuq6jGGBZ7LaWkuKxE2IUa+\naVnt3V1Wc8NPcC44LXm3lYg8Hto8h9RunynA+UfSqCrjwBi76oli87M51Sxb\nLu/gn97Seeuprw8OG/FR9uTsgeJuHF6QorBXoHE5tCjM2t47pGjoqLrvXIpw\nA+JKKpYptF6IRWQKCiw9YVVpqqg+Zo5zUJ5M2KfP8SIKgHHVxSHeJ3pL8sLP\n2zdjKvLbws3RHewnuWzLgzjNQwajybhUk4XnJiOB5y9O172qsVwjCV1/AO7a\nk+YxYctH9FnMSxKaXxoRbBqritQgKRjPObmrEzxK0hWP43GllI3GSn+qEc/p\ndvoK2h8Ou0Wv5RbfuVrdQuOxJzFoTd52zlPUxsjPYNDmZ4ZwHFLTdj+4Q5Ds\nqgaOu/PPN3hFhmejBex3gt6lUnsEdkCtHZnv19vg2PIC+8QEFdkYxpKtASyC\nDzhCFzZgM3I6tHEt6TT/bl44sNjG3h+EZXZqeGfTZtb/eUxB96UMaq7b2gyi\nsIZeLGnJqVq9gY4ODMJA0jjPU34tfI5vgg0FMv/X/4lQMDKWHBZrlRlF0tyA\nzJcf\r\n=y2a4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "f6d0cee68ef397879666e9c7b66bdb9239e43650", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.7.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.1.0_1617443002199_0.4698730633479653", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "prebuild-install", "version": "6.1.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6754fa6c0d55eced7f9e14408ff9e4cba6f097b4", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.1.1.tgz", "fileCount": 17, "integrity": "sha512-M+cKwofFlHa5VpTWub7GLg5RLcunYIcLqtY5pKcls/u7xaAb8FrXZ520qY8rkpYy5xw90tYCyMO0MP5ggzR3Sw==", "signatures": [{"sig": "MEUCIDSaCbOmLvhCFRnWFWLY+Du+xHD/ubH8Y6sFv2qF4/S+AiEA/IuYiPoMlExPJJFZEWiRIdmDGY2Q9bklMKal8lnHWZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgacBKCRA9TVsSAnZWagAAbckP+wcSXaXNCaPMufU5ekOx\ntLk0ABKfDP7R68bPFZMdTxXI+CjxYj/uIl31//l8cBivNAwAX+orCbq2jrHB\nYVF77nsOB/TKXke5O4y0ycH5W0QlDP3Q2pSDtRi06d3PwkmxtiMS4medS2f5\nXAApxem+38XB3EY3HyI3KJnnTEl+LzniGOzRukC8l3+YeAI/bcW6iKntJAmG\nenyFkw14BSEVHhwfYbZuZfqo9G/XImkqxUSRxLhGqjRrbC9imYgt6FZgo228\nmJ11i/0hfpzYju6aJBQfzu1lVPOjrVHYE+pKkyW1jW4mzZqS8lJdLwBbfc71\nqTkkE4WPUTsBYLZOr8XGDPnxN6wdzwszpDLTTNPRsuE5OrwSBJvto6I1yb0X\nYnkQSyCMuAbN8sQcXFiq5e00Mq+cftIG+83bobZWpnNgzowtTaT86jI2DOkL\nKbZ3ynak5QcxkWafVWxW9qHWDIpse05Zhu8xqgImUeOSrAIn2Jm7g9SzMg+5\n42+DMUIEBrE9Dvafy5SQKZ6ZtiNvCJBINLKZ5iJauKuvyMEmwPrU6CDgx9lI\n5TvDj1d+/Yi+EDeOHl8LcGivJuf3BUv5sEWzQ2i4vt29TmuIrHccUIT/DFRO\n+KaCh2zLBszgx2pK9TSQMQF/AZ29oHlN+b1HV3laUo9j5Wn6JJpFqPR34rc1\n1c8E\r\n=peH/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "2e3fedbe5363d510b778529d1eaef8b17692b78e", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.21.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.1.1_1617543241538_0.7540964372634194", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "prebuild-install", "version": "6.1.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "./bin.js"}, "dist": {"shasum": "6ce5fc5978feba5d3cbffedca0682b136a0b5bff", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.1.2.tgz", "fileCount": 15, "integrity": "sha512-PzYWIKZeP+967WuKYXlTOhYBgGOvTRSfaKI89XnfJ0ansRAH7hDU45X+K+FZeI1Wb/7p/NnuctPH3g0IqKUuSQ==", "signatures": [{"sig": "MEUCIBeCGO2VhyeV/KP84J/x7T6TRrR8kxR7Lrm7z3e92HVKAiEAxDvWNgsBRzfswDO2rFzq5oGkVID3ELOSyGOfCstiphk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgg+RkCRA9TVsSAnZWagAAZMQP/3zfyFuKJu7bVv3m+gM0\nw0eNRMOeUJeeuylURCPG6lzwYa/fVadVwyr8yklvhb2zjGwqB2xtQCKEXjo+\nXJczojJCb3V6rHQmQ/9QoglellT326yEfiODu4QW9s70npoRCig1F4vFeoey\nP2thBjL+UBlBVvyp55MV7B8/xyZH10YK+8oEKqflGOI4d2tVqvXBN7R8I237\nz3WfybrasDTIvb/5/mo1wrNPamE4miQ5L2faYbcyykcx+JOD/07TEjox8AQl\nPKGAaJ5A3/xuy+pnxVrBLjIFimjfbPRnB/3ZIzlpNd2/kDnZgzEh8dN70ZX3\n67CsBSnIu7H4e/LXqKjtHCLO98v/GbfBER2/YryylFFgNJAFSLDTs3j3BH2k\nEavBsl+BWobY+720FKHF+p0x8fbnYa9uitnp0oSnzz2ugLvxs7CckbDcZy6d\nzwu2aBigEg3RDdIg5FxZn7phTcxueGpqhqlPgHeSDaczEvQxR2KNPJ3+aUWL\n9Ytkz3uAgWjFZB25P1Jx1L5j7orsKYPvFLJlqiY7eQJvEdcb3W+feRCo41cL\n6EJNDDwlBJrarxR4GTiCiN+CX8FL5zzSio7LJlOjjgRHFeVWpGuW9ux9jeEG\nIi9lK79QlhRDwGBBSa1HOKn1V4KyZ5F1eG+j1Glu2RXmo5NEX5I9w15IcmN8\nRV3s\r\n=wmDF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "97ff071877b1f9350b030be26edd05122a0d97e2", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.21.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "noop-logger": "^0.1.1", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.1.2_1619256420444_0.6588980490739289", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "prebuild-install", "version": "6.1.3", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "8ea1f9d7386a0b30f7ef20247e36f8b2b82825a2", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.1.3.tgz", "fileCount": 15, "integrity": "sha512-iqqSR84tNYQUQHRXalSKdIaM8Ov1QxOVuBNWI7+BzZWv6Ih9k75wOnH1rGQ9WWTaaLkTpxWKIciOF0KyfM74+Q==", "signatures": [{"sig": "MEUCIQDkteBqUk7kfRxccTaXlvY1uLtxQ8yrG3mQBcUQgrksJwIgWx4yPK3+g8Fkq8I2hZpZksVWxtXrTCGntVX6wwVjMn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguI7DCRA9TVsSAnZWagAARxwP/jOWVoSFRnTGF+9BwUaG\n8M/5q+F/gVXlhCKE7yvSJ8mEeD2lx2UzX/pYW8+87RU6wl9Hz0SYifVH99dv\nTIO7UVmnOTX06luBnCCLUJs6V0HaNwbGN0PSncBwDRgHamgk/mzVe+Nf/9v7\nZdvCnx2tm6Xp5OtBwgG/WfiTx7suXdxxDP5r4jtiirT4feB78VupZoBZToPi\nzfB1rOFgJVZ1caaqsDSAVeKXAkXGzabadeUUcC1gIkX6r+9Gxmqual3bmICg\nV0YARcgcWj1F3HhRiqLMKiW5A7hhJ/seEhhtEUJGDHYPBBC64BjV57wir8Dh\n7uKjJlVgwR/aVXYU/wKhSgwuxmoGNOiqeky05de2cdMPI28e04CoSfVdQMyt\nGeKYFc8VhUjszeqDwIcIWx4kJFYrPZHlf0NmP00JsLTfo5GFloxdX5bbQhoY\nFiKZwKfYMsaf4hFts3Li8+I6ohR92mRw8VLqtBaDEFwMg3fJYpD55ueeoPLP\nU0jjq8V65se7iD4byQJMiLs31WmbQOwf7kE2V8NN3lUywX1YEfCLFNuJQ2M2\nc+dNk/yKiqu4iihI8w1z18nDd1MXM2Q6kc4U9yBnoE4i7PrRS5btS1V2XHpO\n2MBEZ/H/ubOFdDNYzjj7JsPAtZ676ZXL3EWv+JUQbdTe3OqGWy3cTro5ThkK\nfO3l\r\n=qwlY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "cca87fbe261756569da5a7022b28eb8da392d9be", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "14.14.0", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.21.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.1.3_1622707907163_0.3193925881466628", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "prebuild-install", "version": "6.1.4", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@6.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "ae3c0142ad611d58570b89af4986088a4937e00f", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-6.1.4.tgz", "fileCount": 15, "integrity": "sha512-Z4vpywnK1lBg+zdPCVCsKq0xO66eEV9rWo2zrROGGiRS4JtueBOdlB1FnY8lcy7JsUud/Q3ijUxyWN26Ika0vQ==", "signatures": [{"sig": "MEQCIAfdQjQosyKhM25MndWvfkxbYxP2uqkEqY9YNg19pFOjAiA0pAaumXhlU3n2eIfIzdRY1PBimrAx5Uzc4v9w3Va8sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFAcgCRA9TVsSAnZWagAA/30P/2mS1CyErRprLvHiZt9H\noEbXpI800W/BANSr26AE1RLcriyWVlZL2rgbJlRezevAj8Rq/CaGk347YPGn\nW9xAGCv2C6yVywA0CR2iNsbRYmcj50vCFrE5lX8JgjDGEITQHM6xXeJmYrQJ\njAFI529vP29MPtoEo4sDy35kFcSxgx+BtbtQz5bmYbCPFWm81vuwbB/1rVB8\njNzBA4+n2Ufm6WXLvXCC35vKmY/g9aRebjVGrhBy6W8bKIDhN8wvhuC8U7TG\nfnv4FWBrLlCNOLF06d2/D7F4TODE5cLS8MmfNHvXo8+F5FgXl/WUFGGkCnbx\nvOb2mPxXacg8bEoJbmSQ616g9NIYn0YSKPj45a2KJArXUoIsQxWZnVkQwfkp\nlgY3f3OegJz/xZKE45PHXhpp7oATRuU1BeqzMG7ItIyOVgpvm7kjfTQUOBUn\nROWIRcfmVO49iFm6/zRrnGx1u5rYlviFfPcZ1QD14jjMy2E70/BxjGK4y4GL\n0bWIFwC21OCbhWCcyEIgc847NRwqzgmL3a5PlEgfm2PYqqs8X098Hjn2z3XD\npTTnN7xQLgHeNzoe0njQGlEfAnN9JpBdKFfvFQsPHncOhrG4DcUEvVs1Hi7C\nsnAy/THW6hAhakTOXPu98LF2DIxfUfZHZpuRbYjXSjMPpk9ePBsr0f5H8biU\n7z/W\r\n=ehjg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "c96c526d360b2e8ce0ece5b9e3c06b13082ea74e", "scripts": {"lint": "standard && hallmark", "test": "tape test/*-test.js && npm run lint", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "12.22.0", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^2.21.0", "simple-get": "^3.0.3", "detect-libc": "^1.0.3", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^4.5.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^13.0.2", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_6.1.4_1628702496216_0.4921113174569314", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "prebuild-install", "version": "7.0.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "3c5ce3902f1cb9d6de5ae94ca53575e4af0c1574", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.0.0.tgz", "fileCount": 15, "integrity": "sha512-IvSenf33K7JcgddNz2D5w521EgO+4aMMjFt73Uk9FRzQ7P+QZPKrp7qPsDydsSwjGt3T5xRNnM1bj1zMTD5fTA==", "signatures": [{"sig": "MEQCIHPDAPoF+5520AIVU54Hl7WoDQ1KICoLxIXeHL0LCW8xAiARbF5Gvi60UCtENQC2CqYOYhwPf+h+eELRj86FszYwrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31482}, "engines": {"node": ">=10"}, "gitHead": "542788b617fc4654d54a51b1d09012e5a3895dd0", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^3.3.0", "simple-get": "^4.0.0", "detect-libc": "^1.0.3", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^5.3.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^3.0.0", "standard": "^16.0.4", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_7.0.0_1636716532014_0.6512054009242307", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "prebuild-install", "version": "7.0.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@7.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "c10075727c318efe72412f333e0ef625beaf3870", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.0.1.tgz", "fileCount": 15, "integrity": "sha512-QBSab31WqkyxpnMWQxubYAHR5S9B2+r81ucocew34Fkl98FhvKIF50jIJnNOBmAZfyNV7vE5T6gd3hTVWgY6tg==", "signatures": [{"sig": "MEUCIQCDevb2yKgXRZSqbtZE67vy+TOrhUzGB1AwFkl+Isk+tgIgFWsxYdW8MF2Eoo0wobMBklZ30cmlKs/kiHoF3DI7j/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh87PTCRA9TVsSAnZWagAAq64P/idgNSk/yvpC8fPxabv5\n/xRdB4DbwJDzebpXX9M2vrssKBIBFjQwQjYyDMIk1xMSuBRJ9+iytb7IVvFq\nypmOo0V2+nn4BPJfXRxVWUazpYq1RpdCzHHwJsvQQmOUBazwFEpE5S0qnrq4\nDfhzdjNt/Xh95si2wFTDfEtFzFYs9Ehszl3jBYBFG7ba93Q8fFngu5jqD0+d\nzU7gC5fmtdiAeXvVDe6pwtD7lv3G7pEbxByw6U70mVLWhkxK9EVUWr8fpcbu\nDTsZlJHjtSbzEaf4F2NvI1GDOHFHDCqBvVZP7Gyc0AbcRrW6wgPOcMYdebBo\nZFbRh5mCgfb4GhO0hLgzoi14zBpJYohxCKTkp5IArEkT7d4azgSThSk8f8A1\ntf1tC8OgPfoihffIFbAcZOUi3qyzsZhYr1xxVGxiOYqz1d4zBzfGlTlzhKKF\nTUUebeLo7uEGqcuQbO65iDulDOIHv0a8L31UqQaySWgVYGIMYhnrtJChN8ht\nia0YH+N4y1MT+IimwLrSUALOWCrPxHFPa8f1Ag1DRdppTA6KmU3kjhq1MSkG\n830C1yMeniUMHb4983ocyXta1bxIxI1ubMGZaNqylv1HbJBppzPkFta6lXPu\n9Refys4pq0JF93yqTjd9qEGrMW1iZszRrFPklw1AmsgVB6MOMnqLh1iLzu+F\n1ruw\r\n=SAY2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "4a1ed436676a6b8b0611272440f0ce5fd0b798bf", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^3.3.0", "simple-get": "^4.0.0", "detect-libc": "^2.0.0", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^5.3.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^4.0.0", "standard": "^16.0.4", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_7.0.1_1643361235645_0.24413793971091025", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "prebuild-install", "version": "7.1.0", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@7.1.0", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "991b6ac16c81591ba40a6d5de93fb33673ac1370", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.0.tgz", "fileCount": 15, "integrity": "sha512-CNcMgI1xBypOyGqjp3wOc8AAo1nMhZS3Cwd3iHIxOdAUbb+YxdNuM4Z5iIrZ8RLvOsf3F3bl7b7xGq6DjQoNYA==", "signatures": [{"sig": "MEUCIDXhiIvrfWPEjJUsRCFjvXNex5JAob+8UimOW1DOsl2TAiEAw2gpRJlJkxZOpeKW+6nQ8M2X8wkLJI4YT5O0xGdNYOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX80iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxaw//S1uOr4RaDEckuCr7Y6HLk/v6m/ex8+TYdtuVfMn9DT84FePz\r\nk1w+6MKVHZUWhYoSsS5wsFJG9V8sB2zRb83Jx0F3luVPa0xeMbT8wa6lQqIB\r\nQwhVLk/q4FjgUfCMLtgFiOhq/c5is3KIiIF5L5ufjO/QQDVLXknMD1b5iZ2Q\r\nOBv5Wajna5kBg0IKxh/FxoW2TKelfp3mpRsptl3tulaYa885C4FjZouEZiaw\r\nrKSLdjCrE1k4iCa2R0SFOu3g/GnYohvChzF1DQeQAPrUo5wNyXi3HJS80zxn\r\nBNjiVSk6sG9UtNATbNvmEZYW0ZwCgxy+HWzvwavFGhZdXqV1ZrUj311FZhgc\r\nCnmwkNcWJavji9+4VwS8mQuXsfMMGkYAg8is0tLzNgUgNqjOddXiCrWIog9O\r\n5aUrKinAZmFzLXn1HjzgXbW/2nZzNeeyDeza8rXGVzbYDkb1FIfLJ2dMd+E8\r\nerx5z13wBm+dNA25WBxlN28JO10Bb5cMHNcYIapUNcj5jpg+YL8oxgXpzPBy\r\nqgDPyPNI/cFQNdcIfrUmgP0zAzKoYnQz+EM5g6IJ8Ol3/oKeFMoheSeeGt/N\r\nZh2QQTXQxr5psekoJ8MOwtHrLjdOBisCaFYBtxy9gZsGgzpbfCaZrUUp6SZW\r\nMbBrQloBysisJ7kp4bxTjQRNu6Qq+F45GPg=\r\n=uLEY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "5065bce028e36b2b67e9195bc48b9a46784b78ea", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "npmlog": "^4.0.1", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^3.3.0", "simple-get": "^4.0.0", "detect-libc": "^2.0.0", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^5.3.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^4.0.0", "standard": "^16.0.4", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_7.1.0_1650445602223_0.7117340085090438", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "prebuild-install", "version": "7.1.1", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@7.1.1", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "de97d5b34a70a0c81334fd24641f2a1702352e45", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.1.tgz", "fileCount": 15, "integrity": "sha512-jAXscXWMcCK8GgCoHOfIr0ODh5ai8mj63L2nWrjuAgXE6tDyYGnx4/8o/rCgU+B4JSyZBKbeZqzhtwtC3ovxjw==", "signatures": [{"sig": "MEQCICeVFBXtUNURJapN7CopxHm8gsyq+gEeSPjasv192DJBAiAfBN5y2luedP4pVjR2Pbz9xZ8xP75mvstfRMFk/K9s6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJin7JzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo05w//ZlsyfWd9ZmCbf5lhH1g9LyzJMGCPkTw/3CvBYSIEBKtFIR4I\r\n+RyWBh1VyZ/jVqgXN89++Qv4ON+eTcTDhI4lUPmpHtHo0+VGswAHNAWJ5s0p\r\nTy84YMS7lYRB351H2yq4E8zV0N+1qERHPK3S3vmsUYlghGJD5HFWVMuCS70m\r\nI/lXQE5ZQJpRyzeS3WV0/rrKmr09w3dU+4QUvCSIU5/tdiPDR8D7vlffpY2M\r\nuEqu2fmH69YB18WrjT3Msx0HbJ3QjTrATSjOPvj5iO5W4TChgshmvts9o868\r\nOUG6rmwPiikLCJlIRMhzJHNOsLJjztMtpV670R/R6ZQp9zPg01fAFul+mkbM\r\nLp63dkN6F8qDyMX0ChMcYAEw6nTN12WsJc9GlxWBcEVD9mKvlKp+QXkNrzRG\r\niZ3DeZ94K/QbbNvhAEW3V32B0BKUjBurJoIyW2l2uBxwINWDgBn4ZQ753fLU\r\nfpvWBLov4x79JWb99RRK80pIJ1BcKTuJgjjq/rdXhA7CWPw8eh6HnUKClIVB\r\nrFiZo0YHWwLd4srSTerxTHH6sN/igLAGwaHlngWn2Y1QH6SF8dz+x2OP8Rnq\r\nTVFwqwpIp4VpvYRw/0dvdQfuREsfnwJOn5ijSKnEjQKgsBgnZjaQLwcwm3xV\r\n4dF5KDGug7/mhoDWYV74nBxHpsTLtUeqV8c=\r\n=dP0A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "8250adf958fcec6fb3ddfcd7dd5ceb0e0e29e419", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^3.3.0", "simple-get": "^4.0.0", "detect-libc": "^2.0.0", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^5.3.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^4.0.0", "standard": "^16.0.4", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_7.1.1_1654633075560_0.49367467930214515", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "prebuild-install", "version": "7.1.2", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "author": {"url": "@mafintosh", "name": "<PERSON>"}, "license": "MIT", "_id": "prebuild-install@7.1.2", "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}], "contributors": [{"url": "https://github.com/juliangruber", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/brett19", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/hintjens", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ralphtheninja", "name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"url": "https://github.com/piranna", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/mathiask88", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/lgeiger", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/prebuild/prebuild-install", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "bin": {"prebuild-install": "bin.js"}, "dist": {"shasum": "a5fd9986f5a6251fbc47e1e5c65de71e68c0a056", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.2.tgz", "fileCount": 15, "integrity": "sha512-UnNke3IQb6sgarcZIDU3gbMeTp/9SSU1DAIkil7PrqG1vZlBtY5msYccSKSHDqa3hNg436IXK+SNImReuA1wEQ==", "signatures": [{"sig": "MEUCIQDUGr5BCLU9jNJNUvx+beaom0rL6O3qPOwUtQMEpwrwMAIgYapWgWWe86uQ4pAzEMXTrfJUgWSLb/Xx8ttzfdk2PVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33884}, "engines": {"node": ">=10"}, "gitHead": "668d7a6a99c9ec08da9b9ecf6549022a3e32f37c", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "_npmUser": {"name": "<PERSON>ll", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/prebuild/prebuild-install.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"rc": "^1.2.7", "pump": "^3.0.0", "tar-fs": "^2.0.0", "minimist": "^1.2.3", "node-abi": "^3.3.0", "simple-get": "^4.0.0", "detect-libc": "^2.0.0", "tunnel-agent": "^0.6.0", "mkdirp-classic": "^0.5.3", "expand-template": "^2.0.3", "napi-build-utils": "^1.0.1", "github-from-package": "0.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nock": "^10.0.6", "tape": "^5.3.1", "tempy": "0.2.1", "rimraf": "^2.5.2", "hallmark": "^4.0.0", "standard": "^16.0.4", "a-native-module": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/prebuild-install_7.1.2_1709232843324_0.4032186593788498", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "prebuild-install", "version": "7.1.3", "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "scripts": {"test": "standard && hallmark && tape test/*-test.js", "hallmark": "hallmark --fix"}, "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "dependencies": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^2.0.0", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}, "devDependencies": {"a-native-module": "^1.0.0", "hallmark": "^4.0.0", "nock": "^10.0.6", "rimraf": "^2.5.2", "standard": "^16.0.4", "tape": "^5.3.1", "tempy": "0.2.1"}, "bin": {"prebuild-install": "bin.js"}, "repository": {"type": "git", "url": "git+https://github.com/prebuild/prebuild-install.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/juliangruber"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/hintjens"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net", "url": "https://github.com/ralphtheninja"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/piranna"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/lgeiger"}], "license": "MIT", "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "homepage": "https://github.com/prebuild/prebuild-install", "engines": {"node": ">=10"}, "_id": "prebuild-install@7.1.3", "gitHead": "aa8e5bae52fa72733663ff6dc394ffbc5cff9971", "_nodeVersion": "22.7.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug==", "shasum": "d630abad2b147443f20a212917beae68b8092eec", "tarball": "https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz", "fileCount": 15, "unpackedSize": 34180, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDGiwXNTSbfa54r4s/Ia3XeH0Hg6d7zzKyc/YdpiZYI0AIhAJ1WuLYC2L4+O88eQhWZkn6eQ37GDUTIvenBPhwtKLvo"}]}, "_npmUser": {"name": "vweevers", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/prebuild-install_7.1.3_1737504519300_0.5412576140824239"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-05-17T14:50:56.451Z", "modified": "2025-01-22T00:08:39.671Z", "1.0.0": "2016-05-17T14:50:56.451Z", "1.0.1": "2016-05-17T21:11:15.948Z", "1.0.2": "2016-05-28T21:08:41.011Z", "1.1.0": "2016-05-30T10:42:04.574Z", "2.0.0": "2016-12-29T13:46:15.423Z", "2.1.0": "2017-01-07T21:01:58.266Z", "2.1.1": "2017-03-02T17:57:06.976Z", "2.1.2": "2017-04-10T08:49:38.843Z", "2.2.0": "2017-07-06T21:48:43.262Z", "2.2.1": "2017-07-22T11:55:46.342Z", "2.2.2": "2017-08-09T11:15:33.031Z", "2.3.0": "2017-10-04T09:46:19.936Z", "2.4.0": "2017-12-06T11:51:36.143Z", "2.4.1": "2017-12-09T10:22:22.264Z", "2.5.0": "2018-01-23T12:09:39.065Z", "2.5.1": "2018-02-08T23:26:37.536Z", "2.5.2": "2018-04-15T22:21:40.552Z", "2.5.3": "2018-04-16T13:22:00.870Z", "3.0.0": "2018-04-30T10:44:25.953Z", "4.0.0": "2018-05-05T10:50:06.202Z", "5.0.0": "2018-07-03T23:08:19.737Z", "5.1.0": "2018-08-28T14:19:12.124Z", "5.2.0": "2018-09-27T19:05:46.408Z", "5.2.1": "2018-10-18T16:57:39.538Z", "5.2.2": "2018-11-28T23:31:19.368Z", "5.2.3": "2019-01-31T20:35:36.904Z", "5.2.4": "2019-02-06T14:11:48.741Z", "5.2.5": "2019-03-11T20:13:18.323Z", "5.3.0": "2019-04-21T10:07:28.175Z", "5.3.1": "2019-09-06T17:09:46.050Z", "5.3.2": "2019-09-12T14:04:39.517Z", "5.3.3": "2019-11-03T22:14:10.525Z", "5.3.4": "2020-05-22T16:34:40.797Z", "5.3.5": "2020-06-26T06:08:28.114Z", "5.3.6": "2020-10-20T10:23:33.232Z", "6.0.0": "2020-10-23T17:25:22.213Z", "6.0.1": "2021-02-14T12:37:07.573Z", "6.1.0": "2021-04-03T09:43:22.403Z", "6.1.1": "2021-04-04T13:34:01.704Z", "6.1.2": "2021-04-24T09:27:00.601Z", "6.1.3": "2021-06-03T08:11:47.285Z", "6.1.4": "2021-08-11T17:21:36.349Z", "7.0.0": "2021-11-12T11:28:52.166Z", "7.0.1": "2022-01-28T09:13:55.809Z", "7.1.0": "2022-04-20T09:06:42.438Z", "7.1.1": "2022-06-07T20:17:55.801Z", "7.1.2": "2024-02-29T18:54:03.521Z", "7.1.3": "2025-01-22T00:08:39.484Z"}, "bugs": {"url": "https://github.com/prebuild/prebuild-install/issues"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "homepage": "https://github.com/prebuild/prebuild-install", "keywords": ["prebuilt", "binaries", "native", "addon", "module", "c", "c++", "bindings", "devops", "napi"], "repository": {"type": "git", "url": "git+https://github.com/prebuild/prebuild-install.git"}, "description": "A command line tool to easily install prebuilt binaries for multiple version of node/iojs on a specific platform", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/juliangruber"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/brett19"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/hintjens"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net", "url": "https://github.com/ralphtheninja"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/piranna"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mathiask88"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/lgeiger"}], "maintainers": [{"name": "<PERSON>ll", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "vweevers", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "piranna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# prebuild-install\n\n> **A command line tool to easily install prebuilt binaries for multiple versions of Node.js & Electron on a specific platform.**\n> By default it downloads prebuilt binaries from a GitHub release.\n\n[![npm](https://img.shields.io/npm/v/prebuild-install.svg)](https://www.npmjs.com/package/prebuild-install)\n![Node version](https://img.shields.io/node/v/prebuild-install.svg)\n[![Test](https://img.shields.io/github/workflow/status/prebuild/prebuild-install/Test?label=test)](https://github.com/prebuild/prebuild-install/actions/workflows/test.yml)\n[![Standard](https://img.shields.io/badge/standard-informational?logo=javascript\\&logoColor=fff)](https://standardjs.com)\n[![Common Changelog](https://common-changelog.org/badge.svg)](https://common-changelog.org)\n\n## Note\n\n**Instead of [`prebuild`](https://github.com/prebuild/prebuild) paired with [`prebuild-install`](https://github.com/prebuild/prebuild-install), we recommend [`prebuildify`](https://github.com/prebuild/prebuildify) paired with [`node-gyp-build`](https://github.com/prebuild/node-gyp-build).**\n\nWith `prebuildify`, all prebuilt binaries are shipped inside the package that is published to npm, which means there's no need for a separate download step like you find in `prebuild`. The irony of this approach is that it is faster to download all prebuilt binaries for every platform when they are bundled than it is to download a single prebuilt binary as an install script.\n\nUpsides:\n\n1. No extra download step, making it more reliable and faster to install.\n2. Supports changing runtime versions locally and using the same install between Node.js and Electron. Reinstalling or rebuilding is not necessary, as all prebuilt binaries are in the npm tarball and the correct one is simply picked on runtime.\n3. The `node-gyp-build` runtime dependency is dependency-free and will remain so out of principle, because introducing dependencies would negate the shorter install time.\n4. Prebuilt binaries work even if npm install scripts are disabled.\n5. The npm package checksum covers prebuilt binaries too.\n\nDownsides:\n\n1. The installed npm package is larger on disk. Using [Node-API](https://nodejs.org/api/n-api.html) alleviates this because Node-API binaries are runtime-agnostic and forward-compatible.\n2. Publishing is mildly more complicated, because `npm publish` must be done after compiling and fetching prebuilt binaries (typically in CI).\n\n## Usage\n\nUse [`prebuild`](https://github.com/prebuild/prebuild) to create and upload prebuilt binaries. Then change your package.json install script to:\n\n```json\n{\n  \"scripts\": {\n    \"install\": \"prebuild-install || node-gyp rebuild\"\n  }\n}\n```\n\nWhen a consumer then installs your package with npm thus triggering the above install script, `prebuild-install` will download a suitable prebuilt binary, or exit with a non-zero exit code if there is none, which triggers `node-gyp rebuild` in order to build from source.\n\nOptions (see below) can be passed to `prebuild-install` like so:\n\n```json\n{\n  \"scripts\": {\n    \"install\": \"prebuild-install -r napi || node-gyp rebuild\"\n  }\n}\n```\n\n### Help\n\n```\nprebuild-install [options]\n\n  --download    -d  [url]       (download prebuilds, no url means github)\n  --target      -t  version     (version to install for)\n  --runtime     -r  runtime     (Node runtime [node, napi or electron] to build or install for, default is node)\n  --path        -p  path        (make a prebuild-install here)\n  --token       -T  gh-token    (github token for private repos)\n  --arch            arch        (target CPU architecture, see Node OS module docs, default is current arch)\n  --platform        platform    (target platform, see Node OS module docs, default is current platform)\n  --tag-prefix <prefix>         (github tag prefix, default is \"v\")\n  --build-from-source           (skip prebuild download)\n  --verbose                     (log verbosely)\n  --libc                        (use provided libc rather than system default)\n  --debug                       (set Debug or Release configuration)\n  --version                     (print prebuild-install version and exit)\n```\n\nWhen `prebuild-install` is run via an `npm` script, options `--build-from-source`, `--debug`, `--download`, `--target`, `--runtime`, `--arch` `--platform` and `--libc` may be passed through via arguments given to the `npm` command.\n\nAlternatively you can set environment variables `npm_config_build_from_source=true`, `npm_config_platform`, `npm_config_arch`, `npm_config_target` `npm_config_runtime` and `npm_config_libc`.\n\n### Libc\n\nOn non-glibc Linux platforms, the Libc name is appended to platform name. For example, musl-based environments are called `linuxmusl`. If `--libc=glibc` is passed as option, glibc is discarded and platform is called as just `linux`. This can be used for example to build cross-platform packages on Alpine Linux.\n\n### Private Repositories\n\n`prebuild-install` supports downloading prebuilds from private GitHub repositories using the `-T <github-token>`:\n\n```\n$ prebuild-install -T <github-token>\n```\n\nIf you don't want to use the token on cli you can put it in `~/.prebuild-installrc`:\n\n```\ntoken=<github-token>\n```\n\nAlternatively you can specify it in the `prebuild-install_token` environment variable.\n\nNote that using a GitHub token uses the API to resolve the correct release meaning that you are subject to the ([GitHub Rate Limit](https://developer.github.com/v3/rate_limit/)).\n\n### Create GitHub Token\n\nTo create a token:\n\n- Go to [this page](https://github.com/settings/tokens)\n- Click the `Generate new token` button\n- Give the token a name and click the `Generate token` button, see below\n\n![prebuild-token](https://cloud.githubusercontent.com/assets/13285808/20844584/d0b85268-b8c0-11e6-8b08-2b19522165a9.png)\n\nThe default scopes should be fine.\n\n### Custom binaries\n\nThe end user can override binary download location through environment variables in their .npmrc file.\nThe variable needs to meet the mask `% your package name %_binary_host` or `% your package name %_binary_host_mirror`. For example:\n\n```\nleveldown_binary_host=http://overriden-host.com/overriden-path\n```\n\nNote that the package version subpath and file name will still be appended.\nSo if you are installing `leveldown@1.2.3` the resulting url will be:\n\n```\nhttp://overriden-host.com/overriden-path/v1.2.3/leveldown-v1.2.3-node-v57-win32-x64.tar.gz\n```\n\n#### Local prebuilds\n\nIf you want to use prebuilds from your local filesystem, you can use the `% your package name %_local_prebuilds` .npmrc variable to set a path to the folder containing prebuilds. For example:\n\n```\nleveldown_local_prebuilds=/path/to/prebuilds\n```\n\nThis option will look directly in that folder for bundles created with `prebuild`, for example:\n\n```\n/path/to/prebuilds/leveldown-v1.2.3-node-v57-win32-x64.tar.gz\n```\n\nNon-absolute paths resolve relative to the directory of the package invoking prebuild-install, e.g. for nested dependencies.\n\n### Cache\n\nAll prebuilt binaries are cached to minimize traffic. So first `prebuild-install` picks binaries from the cache and if no binary could be found, it will be downloaded. Depending on the environment, the cache folder is determined in the following order:\n\n- `${npm_config_cache}/_prebuilds`\n- `${APP_DATA}/npm-cache/_prebuilds`\n- `${HOME}/.npm/_prebuilds`\n\n## Install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install prebuild-install\n```\n\n## License\n\n[MIT](./LICENSE)\n", "readmeFilename": "README.md", "users": {"ralphtheninja": true}}