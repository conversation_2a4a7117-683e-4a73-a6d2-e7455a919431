const { Events } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');
const fs = require('fs');
const path = require('path');

// Message tracking for points
const MESSAGES_FOR_POINTS = 100; // Messages needed to get points
const POINTS_PER_100_MESSAGES = 10; // Points awarded per 100 messages

// Path to message commands file
const MESSAGE_COMMANDS_FILE = path.join(__dirname, '../../data/messageCommands.json');

module.exports = {
    name: Events.MessageCreate,
    async execute(message) {
        if (message.author.bot) return; // Ignore bot messages

        // Handle team points for text activity
        await handleTextActivity(message);

        // Handle custom message commands (with prefixes or without)
        await handleMessageCommands(message);

        // Original Arabic greeting
        if (message.content.toLowerCase() === 'السلام عليكم') {
            message.reply('وعليكم السلام ورحمة الله وبركاته');
        };
          if (message.content.toLowerCase() === 'مساء الخير') {
            message.reply('مساء الورد');
        };
        if (message.content.toLowerCase() === 'صباح الخير') {
            message.reply('صباح الورد');
        }

        // Commands that start with !
        if (message.content.startsWith('!')) {
            const command = message.content.toLowerCase();

            switch (command) {

                case '!مساء الخير':
                    message.reply('مساء الورد');
                    break;
                    
                case '!صباح الخير':
                    message.reply('صباح الورد');
                    break;
                    
                case '!دعم':
                    message.reply('https://tip.dokan.sa/sahm');
                    break;

                case '!x':
                    message.reply('https://x.com/4e56');
                    break;

                case '!اكس':
                    message.reply('https://x.com/4e56');
                    break;

                case '!تويتر':
                    message.reply('https://x.com/4e56');
                    break;

                case '!كيك':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!kick':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!حسابات':
                    message.reply('https://guns.lol/.sahm');
                    break;

                case '!ص':
                    message.reply('اللهم صل وسلم على سيدنا محمد');
                    break;

                    
                case '!كفاره':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  

                case '!كفارة':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  
                                       
                case '!احبك':
                    message.reply('ماحبيت الا اللي يحبك ويغليك');
                    break;  
                    
                        
                case '!س':
                    message.reply('سبحان الله وبحمده سبحان الله العظيم');
                    break;
                    
                case '!صلاه':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;

                case '!صلاة':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;

                // Command to create new message commands
                case '!createcommand':
                case '!إنشاء_أمر':
                    await handleCreateCommand(message);
                    break;

                // Command to list all message commands
                case '!listcommands':
                case '!قائمة_الأوامر':
                    await handleListCommands(message);
                    break;

                // Command to delete a message command
                case '!deletecommand':
                case '!حذف_أمر':
                    await handleDeleteCommand(message);
                    break;
            }
        }
    }
};

async function handleTextActivity(message) {
    try {
        // Excluded channels - no points awarded for messages in these channels
        const excludedChannels = [
            '1347454380093341758',
            '1325734703155904533',
            '1386926777120850053'
        ];

        // Skip point tracking for excluded channels
        if (excludedChannels.includes(message.channel.id)) {
            return; // Exit early, no points for these channels
        }

        const userId = message.author.id;

        // Get current message count from database
        const teamManager = getTeamManager();
        const currentCount = teamManager.db.getMessageCount(userId);
        const newCount = currentCount + 1;

        // Update message count in database
        teamManager.db.updateMessageCount(userId, newCount);

        // Check if user reached 100 messages milestone
        if (newCount % MESSAGES_FOR_POINTS === 0) {
            // Award points for reaching 100 messages
            const result = await teamManager.addPointsToMember(userId, POINTS_PER_100_MESSAGES, 'Text Chat Milestone');

            if (result.success) {
                // Log milestone achievement
                console.log(`🎉 ${message.author.tag} reached ${newCount} messages milestone and earned ${POINTS_PER_100_MESSAGES} points!`);
            }
        }

    } catch (error) {
        console.error('Error handling text activity:', error);
    }
}

// Message count tracking is now stored in database for persistence

// Helper functions for message commands

// Load message commands from JSON file
function loadMessageCommands() {
    try {
        if (!fs.existsSync(MESSAGE_COMMANDS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(MESSAGE_COMMANDS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading message commands:', error);
        return {};
    }
}

// Save message commands to JSON file
function saveMessageCommands(commands) {
    try {
        // Ensure the data directory exists
        const dataDir = path.dirname(MESSAGE_COMMANDS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(MESSAGE_COMMANDS_FILE, JSON.stringify(commands, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving message commands:', error);
        return false;
    }
}

// Handle custom message commands
async function handleMessageCommands(message) {
    try {
        const messageCommands = loadMessageCommands();
        const content = message.content.toLowerCase();

        // Check for commands with different prefixes
        for (const [commandName, commandData] of Object.entries(messageCommands)) {
            const { trigger, response, prefix } = commandData;

            let shouldTrigger = false;

            // Check based on prefix type
            switch (prefix) {
                case '!':
                    shouldTrigger = content === `!${trigger.toLowerCase()}`;
                    break;
                case '+':
                    shouldTrigger = content === `+${trigger.toLowerCase()}`;
                    break;
                case 'none':
                    shouldTrigger = content === trigger.toLowerCase();
                    break;
                default:
                    shouldTrigger = content === `${prefix}${trigger.toLowerCase()}`;
                    break;
            }

            if (shouldTrigger) {
                // Replace placeholders in response
                let finalResponse = response;
                finalResponse = finalResponse.replace(/\{user\}/g, `<@${message.author.id}>`);
                finalResponse = finalResponse.replace(/\{username\}/g, message.author.username);
                finalResponse = finalResponse.replace(/\{server\}/g, message.guild.name);
                finalResponse = finalResponse.replace(/\{channel\}/g, `<#${message.channel.id}>`);

                // Update usage count
                commandData.usageCount = (commandData.usageCount || 0) + 1;
                commandData.lastUsed = Date.now();
                messageCommands[commandName] = commandData;
                saveMessageCommands(messageCommands);

                // Send response
                await message.reply(finalResponse);
                return; // Exit after first match
            }
        }
    } catch (error) {
        console.error('Error handling message commands:', error);
    }
}

// Handle create command
async function handleCreateCommand(message) {
    // Check if user has permission (optional - you can modify this)
    if (!message.member.permissions.has('ManageMessages')) {
        return message.reply('❌ You need the "Manage Messages" permission to create custom commands.');
    }

    const args = message.content.split(' ').slice(1);
    if (args.length < 3) {
        return message.reply(`❌ **Usage:** \`!createcommand <prefix> <trigger> <response>\`

**Prefixes:**
• \`!\` - Command with ! prefix (e.g., !hello)
• \`+\` - Command with + prefix (e.g., +hello)
• \`none\` - Command without prefix (e.g., hello)
• \`custom\` - Custom prefix (e.g., >hello)

**Example:** \`!createcommand ! hello Hello there!\`
**Example:** \`!createcommand none hi Hi everyone!\`
**Example:** \`!createcommand + test This is a test!\``);
    }

    const prefix = args[0].toLowerCase();
    const trigger = args[1];
    const response = args.slice(2).join(' ');

    // Validate prefix
    const validPrefixes = ['!', '+', 'none'];
    if (!validPrefixes.includes(prefix) && prefix.length !== 1) {
        return message.reply('❌ Invalid prefix! Use `!`, `+`, `none`, or a single character.');
    }

    // Validate trigger
    if (trigger.length < 1 || trigger.length > 32) {
        return message.reply('❌ Trigger must be 1-32 characters long.');
    }

    // Validate response
    if (response.length < 1 || response.length > 2000) {
        return message.reply('❌ Response must be 1-2000 characters long.');
    }

    const messageCommands = loadMessageCommands();
    const commandName = `${prefix}_${trigger}`.toLowerCase();

    // Check if command already exists
    if (messageCommands[commandName]) {
        return message.reply('❌ A command with this trigger and prefix already exists.');
    }

    // Create the command
    const newCommand = {
        trigger,
        response,
        prefix,
        createdBy: message.author.id,
        createdAt: Date.now(),
        usageCount: 0,
        lastUsed: null
    };

    messageCommands[commandName] = newCommand;

    if (saveMessageCommands(messageCommands)) {
        const prefixDisplay = prefix === 'none' ? 'no prefix' : `\`${prefix}\` prefix`;
        message.reply(`✅ **Command created successfully!**

**Trigger:** \`${trigger}\` (with ${prefixDisplay})
**Response:** ${response.substring(0, 100)}${response.length > 100 ? '...' : ''}

**Usage:** ${prefix === 'none' ? trigger : prefix + trigger}`);
    } else {
        message.reply('❌ Failed to save the command. Please try again.');
    }
}

// Handle list commands
async function handleListCommands(message) {
    const messageCommands = loadMessageCommands();
    const commandList = Object.entries(messageCommands);

    if (commandList.length === 0) {
        return message.reply('📝 No custom message commands have been created yet.');
    }

    let response = '📋 **Custom Message Commands:**\n\n';

    commandList.forEach(([commandName, commandData], index) => {
        const { trigger, prefix, usageCount, createdAt } = commandData;
        const prefixDisplay = prefix === 'none' ? 'no prefix' : `\`${prefix}\``;
        const usage = prefix === 'none' ? trigger : prefix + trigger;
        const date = new Date(createdAt).toLocaleDateString();

        response += `**${index + 1}.** \`${usage}\` (${prefixDisplay})\n`;
        response += `   📊 Used ${usageCount || 0} times | Created: ${date}\n\n`;
    });

    // Split message if too long
    if (response.length > 2000) {
        const chunks = response.match(/[\s\S]{1,1900}/g) || [];
        for (let i = 0; i < chunks.length; i++) {
            if (i === 0) {
                await message.reply(chunks[i]);
            } else {
                await message.channel.send(chunks[i]);
            }
        }
    } else {
        message.reply(response);
    }
}

// Handle delete command
async function handleDeleteCommand(message) {
    // Check if user has permission
    if (!message.member.permissions.has('ManageMessages')) {
        return message.reply('❌ You need the "Manage Messages" permission to delete custom commands.');
    }

    const args = message.content.split(' ').slice(1);
    if (args.length < 2) {
        return message.reply(`❌ **Usage:** \`!deletecommand <prefix> <trigger>\`

**Example:** \`!deletecommand ! hello\`
**Example:** \`!deletecommand none hi\`
**Example:** \`!deletecommand + test\``);
    }

    const prefix = args[0].toLowerCase();
    const trigger = args[1];
    const commandName = `${prefix}_${trigger}`.toLowerCase();

    const messageCommands = loadMessageCommands();

    if (!messageCommands[commandName]) {
        return message.reply('❌ Command not found. Use `!listcommands` to see all available commands.');
    }

    delete messageCommands[commandName];

    if (saveMessageCommands(messageCommands)) {
        const prefixDisplay = prefix === 'none' ? 'no prefix' : `\`${prefix}\` prefix`;
        message.reply(`✅ **Command deleted successfully!**

**Deleted:** \`${trigger}\` (with ${prefixDisplay})`);
    } else {
        message.reply('❌ Failed to delete the command. Please try again.');
    }
}
