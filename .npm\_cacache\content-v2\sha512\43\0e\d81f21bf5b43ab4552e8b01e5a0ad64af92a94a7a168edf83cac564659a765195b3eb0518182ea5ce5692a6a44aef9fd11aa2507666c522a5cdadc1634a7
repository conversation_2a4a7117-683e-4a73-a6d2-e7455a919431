{"_id": "util-deprecate", "_rev": "10-316416ab420c938cec971364c2735d25", "name": "util-deprecate", "description": "The Node.js `util.deprecate()` function with browser support", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "util-deprecate", "version": "1.0.0", "description": "The Node.js `util.deprecate()` function with browser support", "main": "node.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "keywords": ["util", "deprecate", "browserify", "browser", "node"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "homepage": "https://github.com/TooTallNate/util-deprecate", "_id": "util-deprecate@1.0.0", "dist": {"shasum": "3007af012c140eae26de05576ec22785cac3abf2", "tarball": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.0.tgz", "integrity": "sha512-sTmIWz2UtUfg8kaf6qlicnMn6ghnpMboyWJAv+kgorwAmCHY78TcfaRWfMD8OECWkqVFwyat+r1VJxA4dDfGSA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCri2jrXgd4KNJxbVAHav4n3MwZ3UrEqrKZ5X54Efk2/wIgI85j6D2+98v25iZu+ygxEUNvgmL3J1aehucOqaCD7hg="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}]}, "1.0.1": {"name": "util-deprecate", "version": "1.0.1", "description": "The Node.js `util.deprecate()` function with browser support", "main": "node.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "keywords": ["util", "deprecate", "browserify", "browser", "node"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "homepage": "https://github.com/TooTallNate/util-deprecate", "gitHead": "6e923f7d98a0afbe5b9c7db9d0f0029c1936746c", "_id": "util-deprecate@1.0.1", "_shasum": "3556a3d13c4c6aa7983d7e2425478197199b7881", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "3556a3d13c4c6aa7983d7e2425478197199b7881", "tarball": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.1.tgz", "integrity": "sha512-/f+A7C3gucLtZ6F6z33sFBFxIrry4KPiO4S1r9KrwNv6ABp/T+IHJzzYGRFCzs2RfgTIm8cA3TJuTdc8INlkNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFClzWQOpZ1wLeTBejlQ3EjmoTEqyiS3w8drAjflDL11AiEArVdXKBnfnVWx8KOhvLopIg51cETWWrVnd4GqPiWVvCU="}]}}, "1.0.2": {"name": "util-deprecate", "version": "1.0.2", "description": "The Node.js `util.deprecate()` function with browser support", "main": "node.js", "browser": "browser.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "keywords": ["util", "deprecate", "browserify", "browser", "node"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "homepage": "https://github.com/TooTallNate/util-deprecate", "gitHead": "475fb6857cd23fafff20c1be846c1350abf8e6d4", "_id": "util-deprecate@1.0.2", "_shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "dist": {"shasum": "450d4dc9fa70de732762fbd2d4a28981419a0ccf", "tarball": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8kbTkCOi5K8s9SSUowetM2i82Yn0Fh8ksO5yTwvPaOAIgLZuKcelfvJTrdYwZOm4QxW2K6ilsaa/SRexFQHyGHvI="}]}}}, "readme": "util-deprecate\n==============\n### The Node.js `util.deprecate()` function with browser support\n\nIn Node.js, this module simply re-exports the `util.deprecate()` function.\n\nIn the web browser (i.e. via browserify), a browser-specific implementation\nof the `util.deprecate()` function is used.\n\n\n## API\n\nA `deprecate()` function is the only thing exposed by this module.\n\n``` javascript\n// setup:\nexports.foo = deprecate(foo, 'foo() is deprecated, use bar() instead');\n\n\n// users see:\nfoo();\n// foo() is deprecated, use bar() instead\nfoo();\nfoo();\n```\n\n\n## License\n\n(The MIT License)\n\nCopyright (c) 2014 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person\nobtaining a copy of this software and associated documentation\nfiles (the \"Software\"), to deal in the Software without\nrestriction, including without limitation the rights to use,\ncopy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the\nSoftware is furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\nOF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\nHOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\nWHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\nOTHER DEALINGS IN THE SOFTWARE.\n", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "time": {"modified": "2022-06-28T07:10:45.145Z", "created": "2014-04-30T17:10:32.112Z", "1.0.0": "2014-04-30T17:10:32.112Z", "1.0.1": "2014-11-25T20:08:13.859Z", "1.0.2": "2015-10-07T18:37:40.665Z"}, "homepage": "https://github.com/TooTallNate/util-deprecate", "keywords": ["util", "deprecate", "browserify", "browser", "node"], "repository": {"type": "git", "url": "git://github.com/TooTallNate/util-deprecate.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "bugs": {"url": "https://github.com/TooTallNate/util-deprecate/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"hugojosefson": true, "mojaray2k": true, "lestad": true}}