{"_id": "@types/babel__template", "_rev": "416-75d7adeaea936c02b0c76932aa30463e", "name": "@types/babel__template", "dist-tags": {"ts2.8": "7.0.0", "ts2.9": "7.0.2", "ts3.0": "7.0.2", "ts3.1": "7.0.2", "ts3.2": "7.4.0", "ts3.3": "7.4.0", "ts3.4": "7.4.0", "ts3.5": "7.4.0", "ts3.6": "7.4.1", "ts3.7": "7.4.1", "ts3.8": "7.4.1", "ts3.9": "7.4.1", "ts4.0": "7.4.1", "ts4.1": "7.4.1", "ts4.2": "7.4.1", "ts4.3": "7.4.1", "ts4.4": "7.4.1", "ts5.8": "7.4.4", "ts5.7": "7.4.4", "latest": "7.4.4", "ts4.5": "7.4.4", "ts4.6": "7.4.4", "ts4.7": "7.4.4", "ts4.8": "7.4.4", "ts4.9": "7.4.4", "ts5.0": "7.4.4", "ts5.1": "7.4.4", "ts5.2": "7.4.4", "ts5.3": "7.4.4", "ts5.4": "7.4.4", "ts5.5": "7.4.4", "ts5.9": "7.4.4", "ts5.6": "7.4.4", "ts6.0": "7.4.4"}, "versions": {"7.0.0": {"name": "@types/babel__template", "version": "7.0.0", "license": "MIT", "_id": "@types/babel__template@7.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "747b9dd9d0065879e46b5e53c95fd2cfd65c3fea", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-HMvbFg376qEw8GBwj980+UWsAg75vCqPIjZdLpbjUg9arfq99QnpqG7IvGKiwB2NXSODa1ojeedoVGV8uJ7SGg==", "signatures": [{"sig": "MEUCIEeiUHHCvX5/jkuNYWZmVW/RnjPNwORmcQk0lVKHnsTtAiEA5vdU1EHnJkGj9qPPXXPk0GSNyTTqHdvQYuEUYHh9o94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWPn6CRA9TVsSAnZWagAASuMP/jG4XlnaU/wk3oVdUwjc\ns04Ko2QfywFS5gXbv8zSirA9E8x84TTWwW6scLAWQe7NadeaJauiqJfc6RbZ\nvIGkHCGwEmP7FzJePVYBXRQcpVy1NRcCxGKB4jPut+u55XTgkS7FhYxmTiV8\n+cDqWKfk/HezVqan/F1Ua4Ofoe3WbmEpV+Fm6JDDdXsAsthIwUEvoWVm666k\n4eUXlwsJAMAgqHEH25Lj3CSwn2O8du8DjiZLIDIfCxvFBXWp6fFDhg265rz8\n6CnDBBKNWPxmybyCgjQqAyEF2qeLKKYEw2wpVHzwd6dvKJWd07ECYFkPOXf9\n9whtbHn+AV8J2+Ta1M6+3fj6EsoZbgrQjLd6tmRVbqsYAw1XB+1cc6XskasZ\nMwIOGBZI28PX2HQlCCcnG8aKH32a/KaiRVeoA1zt+zaGipodhpfYhI2/JzIT\nHSEJLIMdVjoJmC7P+H+H0uhcu8ZWPWhUSQTftwSnOaV9C012GFW7wmatkR7U\nhHNTCfYzHo0BScabGT69D/yH1ArQviHWQ/tKp3FXwJiC8WDm9gKIRjzyNuaD\ngIkhRZgnThQWRDXcrewUQZHJmvrlZLu7sIU3ra6B7A4/v8Si6D103hPLi0PD\nujFdLJxSmw3+CxDoxYfa/wlUJwJW43yLlptY/XGBtLOQw5T5nGmSYEXdKCB4\naQ7p\r\n=AgrX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0-beta.54"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.0.0_1532557817696_0.26370957267610695", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ba77852ea1c9492043082a357998ace19ddc359e9a5aa480656ef47411514c33"}, "7.0.1": {"name": "@types/babel__template", "version": "7.0.1", "license": "MIT", "_id": "@types/babel__template@7.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8de6effa729bf0b0f65eb72399b629a3998a7f10", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-LT1zwkpL0/2oBP+npLaOCYSWv47cxbdAvONutjalMdCIBfjtfzVNnT8rgllBmsr15QAdAZDOmYw1CPhLB2JCtA==", "signatures": [{"sig": "MEUCIQDLSW6G1mjjcAjGMlgWqyKMySDeiiWwzpmMtXgaaXbhKwIgbvmQk18M9at/0s3rVjdRNvghwx7Emy3EZdy/Y39Noj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7fo+CRA9TVsSAnZWagAA9RoP/2oX5YxAuJpWVQWzNaFp\nBMirwZqD9QGdTA2/NNOpKi5DAhhgglFNg5Cpl35kCHbOmL/asfKEnbNEXea9\nnTIntIGI9UhF/MsVsBa7JJTfzVe19ClvBFNRjUcNeVBW7rQSVZj4LtAumqLZ\ntjbTYoW88tccTFZgH+k5kmlC+ZAYRIOZ2ZBo5bzBiyJeAUFslaeEANHT9/eC\nL5ewB8iUScRzDJh5X5DUNv8n21KGlYBvVvDRC9QjIbp8teqqOedimSAoCbLt\n9bmykawnQzL5MSCxpMja1chd3qDmRIdME1lFFGWRSyOH4CFa13GgAh8QfWw+\ntnf808TWcTeslMfN2be+JgHZPUE0VM2Xc4n7769td9VwZ5CB4Em/DfS6u2Gi\nv1ICryvDZH7KHDsyXdYny5BqOLwMd1skJK0Xh3CyM8ydecYsGDfkyD8I01Vs\nNgUS6QXB/XTkCtlcsWGhMtwOQhPwWjbVa3dIMGXJTrUcd3/ScBZyGMI6obhn\nmqmO2/WVRFLz3HgzeCzXxtFkEwWeYWaj/4/kJJVAEZgDsLFmFgCx6jpU9UTB\n9yzY+r2hCvQy0ZJuit4skvSX2wA5mfge88cJQKNYN+Zg7o1XZ/8d3us8uBgG\n2qbYdWbw3zDJhVkki+auTPXU8Plnb90Nry6hVEJYf/7FKm/6aA6wRfZY6mGQ\nyS+Q\r\n=0v91\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.0.1_1542322750115_0.8764705622047486", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1d6f0a524fa97caf360f980c3df6834454a13b2789d8ea78798ad1ba8c65fe1f"}, "7.0.2": {"name": "@types/babel__template", "version": "7.0.2", "license": "MIT", "_id": "@types/babel__template@7.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4ff63d6b52eddac1de7b975a5223ed32ecea9307", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-/K6zCpeW7Imzgab2bLkLEbz0+1JlFSrUMdw7KoIIu+IUdu51GWaBZpd3y1VXGVXzynvGa4DaIaxNZHiON3GXUg==", "signatures": [{"sig": "MEQCICs/sA2fUbvnaEFicJusEPiY1CDYP2ixrFTX/9kDbFDXAiAUWl1uCvA1QT4OQWw+Nhnx08kJiaVYGhSU38guaPCZnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZIdFCRA9TVsSAnZWagAAsZ0P/jyLJi0fDLUCKUpdhjqg\ngcuPm8jM2hc6LctAIVwutHYK7zP3oXnPqoHwwQd694T1MqpeOAr9tG53nxzF\nqlu6Au8SFVeVe1XvcDLDys8rmrOPD0hOvd8FLzw4rDwWjlr59/CmhyxrfqnH\nA0IQfb2a5M/LIwNRiJ/BRIjSROSV61B83xW1Sorzu9wUWZfYfq0z+LPK2Kti\nGpQ/+WpbFtnkHx0riq+DDHLUNBfVamEpy8/G2vs+SD+r7iUEo9zU17WLncys\nKDJHLcb/BGIFi6QhxykRI2IJVdoBHAqqtNqCYolBq1ekFz73xSqq8pFxU8Vr\nifYeVmbd/iKro+vs3qbfK1jKr61E/JDr3YdfXhFPNtp5ub88GfZybwu+23wb\nurA7isrxI5znrMvb5M1V0SZL+94CkKCRM5X/bD2fMk1icf3qIVWMsOGaTCWA\nz++qPuG22pVi558429Pc/t1d5M3Ejy332p9zpHm64DJERRLNNXQYtNkVPIBE\nuXdzT9PLCGHLMsXj5625kltlCib52PA1sHjpkaDOT67ZO1MxZPSuEVboAj9Q\nxhoGxK0vKkVnfZBqwiWj68UNzciKBIa521iZmC1XYqSTdUiWewg33u7M5Nf7\naJWSk3Pekld5FswujVPh7Lj8BS0y6Mtg2/YVh0GwTmHVMKI+fi3xZLllfYZP\nSSrM\r\n=cIMc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.0.2_1550092100247_0.3488108213996364", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fd665ffdd94e184259796e85ff1a8e8626a23fb0eeefd6cfb9f77a316eda2624"}, "7.0.3": {"name": "@types/babel__template", "version": "7.0.3", "license": "MIT", "_id": "@types/babel__template@7.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b8aaeba0a45caca7b56a5de9459872dde3727214", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.0.3.tgz", "fileCount": 4, "integrity": "sha512-uCoznIPDmnickEi6D0v11SBpW0OuVqHJCa7syXqQHy5uktSCreIlt0iglsCnmvz8yCb38hGcWeseA8cWJSwv5Q==", "signatures": [{"sig": "MEQCIE51+3agqqsZLHIDqnzGQXwvjB1EmVwLb/O0lsVmmjEGAiBzK+xan29dtl0aumBUE5WpTLc2XQwJfTh+EiEy1AZqgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbQZ6CRA9TVsSAnZWagAAIA8P/2MWulUvV6W1jgf4eqen\nj08QqQzBN+e41mlu8SraFSJXLP/l+4LRdcFNDBxJ47xQnnOzlcVfCWslgPH7\nkpvfeD04KwAidKgIzu2/8cgWsJ3YntrvV6AbD5w5UZsXTURVOgZG8/CM2pKS\nJASSKQfRcwzVN+Ya0rPbikKOgJAaQUBDcPvpK20Ekrqklg/i7Ur29B5C5eCk\n6YXq2uCuYSbYahPXW8rHFJiwMPddNTFNYuhJSVQysN8FcZPXAvwiOfABamLY\n87G6tZWmXaa1NQQbFqaRTJxhv82KadakkvyFLfI/GbwPaOPDTfOhtKQOe2Wr\nzxRwXKPEHBPddH/2F4yTdwLaFGQuN6ciWltfJfE1YzgdYOQHiZ7+U9B0ghW7\ncZV7Xi4DjNeh9G+BQFX8FdBrVXqG4mNwpoBCpZBHAs7SxXvJlfmEpQgPx0FK\nKGfQUHWvJ/Eoe/iWg9oWZMQSqoB1iOQS2HHrgJk5kDtvhttEuI2bZdr2tVUO\nt2cws9w/mmRYLDTkxX0E+vCLnPcmDxLcHYa79z5rzRb1dnDYbFcWn7xlRl8f\nBqRrfyAscToDJb6AnudM7fCKI0EcSiyLPFwmYeAZNBDJsUNTWMTI6xpQYLK6\n3hzyDp9y+719mvv9RzgZNej0oQZVhlRNTVKSM2iWLNH+eHyYTODpv6WWoEgi\nk7LG\r\n=plic\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.0.3_1600980602420_0.8397350230052223", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "726cb6c3eb7a24a445b718bb55b7b52198a7818b525a717728b7eb3d741bdb79"}, "7.4.0": {"name": "@types/babel__template", "version": "7.4.0", "license": "MIT", "_id": "@types/babel__template@7.4.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "0c888dd70b3ee9eebb6e4f200e809da0076262be", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.0.tgz", "fileCount": 4, "integrity": "sha512-NTPErx4/FiPCGScH7foPyr+/1Dkzkni+rHiYHHoTjvwou7AQzJkNeD60A9CXRy+ZEN2B1bggmkTMCDb+Mv5k+A==", "signatures": [{"sig": "MEUCIQCgVc48QF/mUqu/Qc69+zEYWros6KkRJg1TD8I8s4d+AwIgXfueZ791KseqMPkWqfRT+ZRAMin2ueMlWMSQOiGtXXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfraBYCRA9TVsSAnZWagAAGdEP/RduCxWVxJKqS0hZGq3d\nOArOUO3oTRTwcWE/05J8Xs/dr4tFXK/JFI4dTUPqHf/MBAVbxiKZtLZUILJx\nqJ3I5IcDDD5FT18b6cr/I5fcsjtG2pvA3xAWk/0pdIxqqCBEznCk2B06NUC8\nvMeWkrcGQtxFfiK5jaCyudtxirP7eUpi8nstQiLHgdxUbjIsi4FFIkJdu+Ja\nAufFw2YSzbR7vESSMrWzr6pxWrCAoPhpLY8sIvBqkiybU+O3H7vrvYyo9ggX\nOt8rgOc3N0u1gZwegxLN2fI8DNOJVpXfXC6PAKYhptte+F0e4DqxEhJwJPgt\nw6UqmPInGr5HzKKqldFyh9pI7W8tjmqz2y27b2a/O+ow/RUlzms2eSxdP5c4\nivE91kEbQ0C9ySrCcvUuFq2n9IPZT8/4GaW9d2GFXh2h00YIxoN0zBzlOw6Z\nDjVC0wFD9cZb+evjaODzra/rJ55aW3a7IO+AeBqHB38Y65NJh12H6sQASrWv\ncwY3+ToReyHi0y8pKs3wbEdz/Tw09L5gieKeUTht1ofHZdU/Kx8vTb4Bf+pu\nLJUfIPWQFiQrajvueRbDfFFnqXyffc5P33wIQUW2dqj8cxmY2ETWeJrUM4dS\nDmFIORHdcRauRsYvYc12kAjky9+dCPJHP7al6Fd1we//g+CIfXI62og0qEHQ\n9lB9\r\n=T3jI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.4.0_1605214295737_0.6822348310324713", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dd8063acb40cd7a72bf4860b78c6c992da2e8af35742f5d50e0a13ddbf1520c0"}, "7.4.1": {"name": "@types/babel__template", "version": "7.4.1", "license": "MIT", "_id": "@types/babel__template@7.4.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "dist": {"shasum": "3d1a48fd9d6c0edfd56f2ff578daed48f36c8969", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz", "fileCount": 4, "integrity": "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==", "signatures": [{"sig": "MEUCIQCRN0rsG6bq1gvHxf6nIqJ3NKmL3atr8+kneh1HlANWNQIgDbAsbSjKSIPXniqDW5J8ZSgBaY9cNr7ZzP3LlkIh4v4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5J4NCRA9TVsSAnZWagAAht4P/iibxc1GcUd3n/bLkQiF\nsY/AALbvFWqzRrlSbnEzg3MYgKR2OaayNwyMtNAF9GmHxYaelJdzsGoOzuNI\n1SiN/qXg3tg4k6RAk+KPPtJZTI0lhQx7XtDdPe3Y4tZfIGLx78tw2N1rHAq9\nKSGxM3JXNXp+4RdUt/ZRFKdKBIDjkbZA+BnR7zAbkQ7nf8raKgVgJ6NS1H/Y\nrsPgUD52XX4ehti9bjfDwopnNjIcRb7QFUArFynR8PoEbJDqFwrsnA6eEuPA\nQMZ5uTaeWj1ePnb4CWdADhcN0WceG+4q14maOUWUun3ucvAhwSX0TlgnL8qy\n0Wm6dSKkbX6uiXcNUmY+dKJfHhW1cqhDn7Zo6whCiHEB2Ey1kUVq/u8pi5l7\noNt30/IGrSUef8+ExnpvVbaTwwv+ErLIqo8MfRgEVVpUki9h0NY0X5X0mnta\nVNBk1F3kXaZOdNGE88q4JfasYaYLFTcN/ydRYXmoEZ5nU+r/kyb00OozXrIo\nYjgtEC/xD8GN5KDP8cXEV4WbCsTV0El3ogwE+3lBsTIjEoknvPqqgSIBZYEK\ntyWM3EPi0pAlcl5qQSV2X2Ak4I8Ekyj1ZTww2qaCAPNZQtA0qG775AD2hZho\n+PMlA43Zzk5sC4ltrs8voWgZeVeRtoC/8EP5BFekFF/dZWSVVSMXnfz6D3jK\nCaNJ\r\n=HEJC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.4.1_1625595404945_0.7468772542698352", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8b488d3b882af8da43e3bb35fc9efcf3bd5fdaec465b98ed620d47b2231b62ed"}, "7.4.2": {"name": "@types/babel__template", "version": "7.4.2", "license": "MIT", "_id": "@types/babel__template@7.4.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "dist": {"shasum": "843e9f1f47c957553b0c374481dc4772921d6a6b", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.2.tgz", "fileCount": 5, "integrity": "sha512-/AVzPICMhMOMYoSx9MoKpGDKdBRsIXMNByh1PXSZoa+v6ZoLa8xxtsT/uLQ/NJm0XVAWl/BvId4MlDeXJaeIZQ==", "signatures": [{"sig": "MEQCICWlwujylWQniRghQinqi5/THU29IaOw9KtJgCMDjYEtAiA5Ke+qK5r2aHuGjwgcoVezMtBzw+127fReSA+n+BLToA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6935}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.4.2_1694805067538_0.9061198808640576", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "32ae250e9a81299d0f3e3bbfe6208be500757c5e8e710617048ed4dbf12419c9"}, "7.4.3": {"name": "@types/babel__template", "version": "7.4.3", "license": "MIT", "_id": "@types/babel__template@7.4.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "dist": {"shasum": "db9ac539a2fe05cfe9e168b24f360701bde41f5f", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.3.tgz", "fileCount": 5, "integrity": "sha512-ciwyCLeuRfxboZ4isgdNZi/tkt06m8Tw6uGbBSBgWrnnZGNXiEyM27xc/PjXGQLqlZ6ylbgHMnm7ccF9tCkOeQ==", "signatures": [{"sig": "MEYCIQDNWZMeGXYLWIwJUPQ1vB46jQGqOdu6rcUpyQoSRQZgLQIhAK9G9lwL36o7qR8HuruJNn83vVYeHt2XL6hO0vjVmnly", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6409}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.4.3_1697583393776_0.7542681317203301", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d654f1c99271fbe2033507014b69ebb24941982683f3f92e85f5c3180017dd13"}, "7.4.4": {"name": "@types/babel__template", "version": "7.4.4", "license": "MIT", "_id": "@types/babel__template@7.4.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "dist": {"shasum": "5672513701c1b2199bc6dad636a9d7491586766f", "tarball": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "fileCount": 5, "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "signatures": [{"sig": "MEYCIQCesKb+IH+ZTEwOHGLibgZ4SogMbD8Dp2+AdtHWCPtDFgIhAOKI9iSnVbEvuSHJBVrgf4RQCguQ+/YC4XB42UhOfpkH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6409}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__template_7.4.4_1699314008394_0.27733975118215004", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5730d754b4d1fcd41676b093f9e32b340c749c4d37b126dfa312e394467e86c6"}}, "time": {"created": "2018-07-25T22:30:17.471Z", "modified": "2025-08-03T06:25:56.885Z", "7.0.0": "2018-07-25T22:30:18.240Z", "7.0.1": "2018-11-15T22:59:10.282Z", "7.0.2": "2019-02-13T21:08:20.448Z", "7.0.3": "2020-09-24T20:50:02.605Z", "7.4.0": "2020-11-12T20:51:35.867Z", "7.4.1": "2021-07-06T18:16:45.084Z", "7.4.2": "2023-09-15T19:11:07.690Z", "7.4.3": "2023-10-17T22:56:34.002Z", "7.4.4": "2023-11-06T23:40:08.640Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__template", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__template"}, "description": "TypeScript definitions for @babel/template", "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}