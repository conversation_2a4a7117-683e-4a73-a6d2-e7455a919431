{"_id": "@types/istanbul-reports", "_rev": "511-be194c380bb854f0dee65061e95d5004", "name": "@types/istanbul-reports", "dist-tags": {"ts2.4": "1.1.1", "ts2.5": "1.1.1", "ts2.6": "1.1.1", "ts2.7": "1.1.1", "ts2.8": "1.1.1", "ts2.9": "1.1.1", "ts3.0": "3.0.0", "ts3.1": "3.0.0", "ts3.2": "3.0.0", "ts3.3": "3.0.0", "ts3.4": "3.0.0", "ts3.5": "3.0.0", "ts3.6": "3.0.1", "ts3.7": "3.0.1", "ts3.8": "3.0.1", "ts3.9": "3.0.1", "ts4.0": "3.0.1", "ts4.1": "3.0.1", "ts4.2": "3.0.1", "ts4.3": "3.0.1", "ts4.4": "3.0.1", "ts5.8": "3.0.4", "ts5.7": "3.0.4", "latest": "3.0.4", "ts4.5": "3.0.4", "ts4.6": "3.0.4", "ts4.7": "3.0.4", "ts4.8": "3.0.4", "ts4.9": "3.0.4", "ts5.0": "3.0.4", "ts5.1": "3.0.4", "ts5.2": "3.0.4", "ts5.3": "3.0.4", "ts5.4": "3.0.4", "ts5.5": "3.0.4", "ts5.9": "3.0.4", "ts5.6": "3.0.4", "ts6.0": "3.0.4"}, "versions": {"1.1.0": {"name": "@types/istanbul-reports", "version": "1.1.0", "license": "MIT", "_id": "@types/istanbul-reports@1.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "3cb3f3ee82160d735d5c067318a110c4a0a02ac9", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-1.1.0.tgz", "integrity": "sha512-wrJUtE1+HuaRz0Le7fc5l1nMTermRh6wlEvOdQPilseNScyYgQK8MdgDP2cf/X8+6e1dtsX/zP4W4kH/jyHvFw==", "signatures": [{"sig": "MEYCIQDeVuOIebR8SP1s0HHnqpbgJqPwFbGp6aJ4/sMeadiBxAIhAPYqDVmNCx6BP34Fb/y1Uw1LJK2Vys66+pNIIpR2i77z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*", "@types/istanbul-lib-coverage": "*"}, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports-1.1.0.tgz_1504188372294_0.9090217791963369", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d9d2c95741344b1fc51f5ff45d6cc90355bca45e1242a9f394a97459dfbf7981"}, "1.1.1": {"name": "@types/istanbul-reports", "version": "1.1.1", "license": "MIT", "_id": "@types/istanbul-reports@1.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "7a8cbf6a406f36c8add871625b278eaf0b0d255a", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-UpYjBi8xefVChsCoBpKShdxTllC9pwISirfoZsUa2AAdQg/Jd2KQGtSbw+ya7GPo7x/wAPlH6JBhKhAsXUEZNA==", "signatures": [{"sig": "MEYCIQCuoyR6RS65iy8yAaUxbLmu260Euo93DcKyOi+fmXExwAIhANCeyyhd6ZLZwitm2SgEUa+vJVj3u6owXgawOQuxxwRy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJct18mCRA9TVsSAnZWagAA5AMP/0Dl+fvvG9NNxVqDbb99\nfsCdUt7KFYbuJBj5smKP5ipZR+u2qpJun9/FfzCLdD+hV4I6mdYxLPv1wW9J\nbPbE01gvMC4vXaggF1xfKl8gY47VZC7NKRevpbgklGZ0sFLgFN8RyoZj5/yu\ngdQBcJw89olMvdV+maPkYxAArZeT5hJabP6Ub7CLZkqIJL8z+6zCQ+7NKBh9\nIxsSRXdE5dZ1ayQxcbSAes78vp+/oEje9+PMGLSLq30qFk2701rIKJMh8fzK\nAvcN2hNbW1+hiBi8CFIYlofy5tcs+GMNNNKFokAyb06KlP+u6Oc9yRw+R9++\nspCseDw3qg9Iq/rbuyIFvteF66e+gDKIKiyHLsATNsISNxUc1JpKcOaKLd3K\nSFV192KCOFoefJ1cDOR3J1kn922ZnEeBONGkDkoTeMV9C+nwJxlH/9Vw7340\ngta5deZSw+qec2zAy9LQTJWqAdDFptaARmqD+3+kyBGOohGifjUWrqsvIjjE\nFK+fDM4XDN45qAKSrBlNVNeUDNVy2AbildTy4fr9m3kgzU01U551b3hK1cWl\naWO5Tgi8qJKBbbEnebISN/1Yi7sZiQLR7RJTLpAUmgvCJuxJPfywfRBaWDDA\nBEldCFhN6vJOE0w/fjxS/eAxjDKCx7MWd1XJaWudcR9Z+CcTQB0z/+WR4O6g\n4yQO\r\n=yUlK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*", "@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_1.1.1_1555521317838_0.3673005123305597", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "48ffb8b28b9f445ebd12c748ea4cf877e1b802bee7fa18c4392b793e84bfce5a"}, "1.1.2": {"name": "@types/istanbul-reports", "version": "1.1.2", "license": "MIT", "_id": "@types/istanbul-reports@1.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "e875cc689e47bce549ec81f3df5e6f6f11cfaeb2", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-1.1.2.tgz", "fileCount": 4, "integrity": "sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==", "signatures": [{"sig": "MEYCIQD0NnOn/tEuFBY5KURhQdXC3YGu9FtMbzZS93YXoH5oFAIhALzFMyWI/USJmvcbpvp1+4OGyMiek5A9/3TWSUbr+H6u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeviwOCRA9TVsSAnZWagAAOK8P/jmZnTKq7cTHV3otV+lP\nwui0D4Sn1eQ/J48hl0Gf/xhVWOLm0wA+0SU17MzWGxesje6pLPBpaUjDGLZC\nRUZW6PnCi79UfyicejD9OrMTenGGPptjiKzWSEzNu0zjnYTfX/nKIw4shBUn\n3KBv3al2bBaUfM813FjB0zQ/QdjwMhWp4DHCIM+SwQbowq6C5nGWQpHHTJao\nQ9UFZ4sNEyoZyuuN6Q355StB8IIncVUdqRTT2P2lt7lBeiw/m7fkQtKblNJw\nDYnUgwZD39uQAqJGWFzug7o0tidYeN2vG7UyySrJaT5gbVfdNbF5mFYkrNP2\nQSTy1busQvlUdS+PLo/mqQsrSu6rmft4Uxu3Sokce4Y4YvonA+Rnf4TUiIOc\nblYVN6506nbLlMcnOw/uryQ0eEvO6Sm/Y1l0QWM9c4LImcRIdbRgSzjikXGk\nlbwjEVndZj43L5NoQLCdDK7FyCIx2bxM9axwONj+wE05q23HCedacTjKzbMs\nfZyth+6LbUICUzD0QCpGlMARkb240d+HOoD27tR2jUNlazZlqVTo88oijnt5\nXVwdJgKEFylp6wkUGzsymr844l3cz6SDVLfqWYPvHs/W4yBXLx8Bfvo+LMB2\nu6Zq2w4nTXYEoXnQ9mixyePX+z+vDdc9w7X6hZPmXiXx0QxPA9WePNo6Rsq9\nvx34\r\n=LvtD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*", "@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_1.1.2_1589521422150_0.7866515831944421", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c13cd090c027208710520a039ec004ef0045ea12516dc4c71d648e4fce9ff9f7"}, "3.0.0": {"name": "@types/istanbul-reports", "version": "3.0.0", "license": "MIT", "_id": "@types/istanbul-reports@3.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "dist": {"shasum": "508b13aa344fa4976234e75dddcc34925737d821", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-nwKNbvnwJ2/mndE9ItP/zc2TCzw6uuodnF4EHYWD+gCQDVBuRQL5UzbZD0/ezy1iKsFU2ZQiDqg4M9dN4+wZgA==", "signatures": [{"sig": "MEUCIQCD6so7JEgJ2mCeEuUVolviteQqfpvtfcP02GE79ySZJgIgPqbb4ggnJDy4bJ0ThU+QhB8ZKrmGk7e+p74jYq0138c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFhMKCRA9TVsSAnZWagAAwBAP/2nv8SKe5seGJTQLE/9q\nRYvcT6Jr2MlRo9rUUAnFMbNAXp/cwOmT44euI8vR/kv4e97c7lNbcU6Oj8FO\nkB8Opn/196U/1AC3FfYKEJPKw6t3A0XSWxS2iYO9eYUffvNCix8UMiHACQiE\naBBAbjKLGuKdQREk73ITQLPrnCel8aQaAj2suNAt++W33wjz7WNuVz7BOVi+\nzVa1Zozb0IEtTDUqK1rz0CXN8dJswJeXOAyaSaxn4ZCuVH6Nrkqt6ht06bCO\nL9uNsDbdWoDZveXtsEraY0vEzhARMKfKa2Cc4frYP7aUebc/Fez4o4DoHV35\npnhkGH3WxdFg2z7fKJNvFkEaHV2fokG7oRzkcpImtmbb4D8ewfmu6si+dvcd\nSlNQRw453MJmFgtIj5NY5IHj2lx3OORA4vG0lV0pEg9XfEWHhqYZaZ9t2VB4\nCcMDK3YegtSkWCSCeE8gR8b+/66k2yHIjgZFrxF7q5O4FmXBRwU/noNkGOz0\nx+Fl6wUy/8oXwb+p1//f7Momd/oiAAFi2HgQ3TKUeudgcyXWSL1zz7TguBVS\n1wBS90Ri0ta38Xkq+e/RBOqQDX0nrpN9d8Wlmv3zn//ENkSTS3oVwIBXJYFN\naWB4pjtifbMv9tuahRch8bWEv4JaSw7gy4eomYoJXA/sO5iXMMvx0DjCoAOY\nPYF8\r\n=GeJa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_3.0.0_1595282185682_0.7301954582051813", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "71342edcc57e7212d17e794fa519955e496dd3b6696e2738904679ef3aa59d70"}, "3.0.1": {"name": "@types/istanbul-reports", "version": "3.0.1", "license": "MIT", "_id": "@types/istanbul-reports@3.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "dist": {"shasum": "9153fe98bba2bd565a63add9436d6f0d7f8468ff", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==", "signatures": [{"sig": "MEUCIQCKNYO1W4PhIwIqB4iOuamEZN6iQsB4Zijr9sRByesSJwIgEJt92ybDvczHCSTJmtyjGFuxwEa9RCmVI112YfFsUkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtqCTCRA9TVsSAnZWagAACvMP/2xB4VT39s2wn8D542uq\nbgaDDge8SaU3T/VYltZ84nEXD2/TVLvQzPgB329jfFTxYPNihpU0QlaxiAB6\ncUuwUNNy5Nl5O+In4i1Cwu0+IiQrAYU6q11TNe3+TXAjYLCmJsXtOjdkOshd\nLe42SB7bKZPzSfOck9dUzBTbcnzmLWl3bNq1AfGxb8vaHv9cUo+902OdK4fl\nVrpUTxj2v5MJlgZNDCvZBuOfeEBuA7BaLNPclkkfO0h6yHymeA4xji06wyow\nRn42B+a6MaOOdMcgLmvfdl6RYIvRQRNPRUFRdnAzxDKNwd3EDzwtf0TUVHpS\ngUFvQdMrppA/BZy72AipsQbZxSkb7EsF6OG41Zt2pFA8vNzYa1oK4UWJT7Ky\nA4GOUgDaWXe80azBgxRxIjmGGa5BUeiU8MdzVUKsEpv6706LMKZQ5nLyzUlG\nS1zWzZzqoB3cyfkyybA2YhifzVxDRK2+6TwDA+TE2lcxRT6VDmk5AFS+DDQH\nZd451LYDqDeHv5srZb1qP1kVWCYGyqfxhiC9VTBtOJx4+bC1QbOUJcSzJqKw\n9CH6bsZo0fC+/3giUTBYAeJngi2nk9e2RISUe8NZ25vyI4KE0DmuwUilKSVx\niPjUBVF2HFIdwe+BACBpxUixRUKUmdGd+WeCctxQxC9bLpQ5L19y5AG6y2Ky\nmBVf\r\n=2CGg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_3.0.1_1622581395274_0.49211175494052317", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b331eb26db90bca3bd6f1e18a10a4f37631f149624847439756763800996e143"}, "3.0.2": {"name": "@types/istanbul-reports", "version": "3.0.2", "license": "MIT", "_id": "@types/istanbul-reports@3.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "dist": {"shasum": "edc8e421991a3b4df875036d381fc0a5a982f549", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-kv43F9eb3Lhj+lr/Hn6OcLCs/sSM8bt+fIaP11rCYngfV6NVjzWXJ17owQtDQTL9tQ8WSLUrGsSJ6rJz0F1w1A==", "signatures": [{"sig": "MEUCIQC4MYCzTgVDdJIOQkgSFClwdW2HKTY0Zrd4pVzoXlvPTAIgGbULDAeHwE5VMsMoRy08BNnaoWMF3wZZMLH81sz0yhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7415}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_3.0.2_1695741235650_0.40100495328337016", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b31e3fc4b65f2465fdc81c31292339ba5c4e35e949aa0413cccf50698291166d"}, "3.0.3": {"name": "@types/istanbul-reports", "version": "3.0.3", "license": "MIT", "_id": "@types/istanbul-reports@3.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "dist": {"shasum": "0313e2608e6d6955d195f55361ddeebd4b74c6e7", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-1nESsePMBlf0RPRffLZi5ujYh7IH1BWL4y9pr+Bn3cJBdxz+RTP8bUFljLz9HvzhhOSWKdyBZ4DIivdL6rvgZg==", "signatures": [{"sig": "MEUCIQCIqp2ezOWt+sfozevVkha+bmZUlJ2+qiABuAhnoQFcuQIgPBfilkIfFeXINHX8iG5VojmzKjAF7/r0bFABKCf6SQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6681}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_3.0.3_1697607000279_0.001740901614697643", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b68383d8d060cbd2ade0f20ed6d864a3cef14e9f9d321965fe0b8012ee15e5ce"}, "3.0.4": {"name": "@types/istanbul-reports", "version": "3.0.4", "license": "MIT", "_id": "@types/istanbul-reports@3.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "dist": {"shasum": "0f03e3d2f670fbdac586e34b433783070cc16f54", "tarball": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "fileCount": 5, "integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==", "signatures": [{"sig": "MEUCIQCPLslmLaZ67JJAVG9fLCfQUKyJIaUPkztdhQybbpZP4wIgO6zo2NEetlUQVjIpPFsn/JYYTSZIBO80txmk2omiMf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6681}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "directories": {}, "dependencies": {"@types/istanbul-lib-report": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-reports_3.0.4_1699344686348_0.5972827251564854", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27b4219ea922d9218dd987cb99b49d7fc77c568322e7102565050323987fa6db"}}, "time": {"created": "2017-08-31T14:06:12.369Z", "modified": "2025-08-03T07:02:07.321Z", "1.1.0": "2017-08-31T14:06:12.369Z", "1.1.1": "2019-04-17T17:15:18.016Z", "1.1.2": "2020-05-15T05:43:42.357Z", "3.0.0": "2020-07-20T21:56:25.787Z", "3.0.1": "2021-06-01T21:03:15.498Z", "3.0.2": "2023-09-26T15:13:55.880Z", "3.0.3": "2023-10-18T05:30:00.476Z", "3.0.4": "2023-11-07T08:11:26.604Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-reports", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-reports"}, "description": "TypeScript definitions for istanbul-reports", "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/not-a-doctor", "name": "<PERSON>", "githubUsername": "not-a-doctor"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}