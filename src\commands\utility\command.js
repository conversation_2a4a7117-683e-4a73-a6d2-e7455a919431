const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('command')
        .setDescription('Create and manage custom slash commands')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new custom command'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all custom commands'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('edit')
                .setDescription('Edit an existing custom command')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('The name of the command to edit')
                        .setRequired(true)
                        .setAutocomplete(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete a custom command')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('The name of the command to delete')
                        .setRequired(true)
                        .setAutocomplete(true))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'create':
                await handleCreateCommand(interaction);
                break;
            case 'list':
                await handleListCommands(interaction);
                break;
            case 'edit':
                await handleEditCommand(interaction);
                break;
            case 'delete':
                await handleDeleteCommand(interaction);
                break;
        }
    },

    async autocomplete(interaction) {
        const focusedOption = interaction.options.getFocused(true);
        
        if (focusedOption.name === 'name') {
            const customCommands = interaction.client.handleCustomCommands.loadCustomCommands();
            const choices = Object.keys(customCommands)
                .filter(name => name.toLowerCase().includes(focusedOption.value.toLowerCase()))
                .slice(0, 25)
                .map(name => ({ name, value: name }));
            
            await interaction.respond(choices);
        }
    }
};

// Handle create command subcommand
async function handleCreateCommand(interaction) {
    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to create custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Create modal for command creation
    const modal = new ModalBuilder()
        .setCustomId('create_command_modal')
        .setTitle('Create Custom Command');

    const nameInput = new TextInputBuilder()
        .setCustomId('command_name')
        .setLabel('Command Name')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter command name (lowercase, no spaces)')
        .setRequired(true)
        .setMaxLength(32);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('command_description')
        .setLabel('Command Description')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Brief description of what this command does')
        .setRequired(true)
        .setMaxLength(100);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('What the bot should respond with when this command is used')
        .setRequired(true)
        .setMaxLength(2000);

    const firstActionRow = new ActionRowBuilder().addComponents(nameInput);
    const secondActionRow = new ActionRowBuilder().addComponents(descriptionInput);
    const thirdActionRow = new ActionRowBuilder().addComponents(responseInput);

    modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);

    await interaction.showModal(modal);
}

// Handle list commands subcommand
async function handleListCommands(interaction) {
    const customCommands = interaction.client.handleCustomCommands.loadCustomCommands();
    const commandList = Object.entries(customCommands);

    if (commandList.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ffa500')
            .setTitle('📝 No Custom Commands')
            .setDescription('No custom commands have been created yet. Use `/command create` to create one!')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle('📋 Custom Commands')
        .setDescription(`Found ${commandList.length} custom command(s):`)
        .setTimestamp();

    commandList.forEach(([name, command], index) => {
        const createdDate = new Date(command.createdAt).toLocaleDateString();
        const usageCount = command.usageCount || 0;
        
        embed.addFields({
            name: `${index + 1}. /${name}`,
            value: `**Description:** ${command.description}\n**Usage:** ${usageCount} times\n**Created:** ${createdDate}`,
            inline: true
        });
    });

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

// Handle edit command subcommand
async function handleEditCommand(interaction) {
    const commandName = interaction.options.getString('name');
    
    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to edit custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const customCommands = interaction.client.handleCustomCommands.loadCustomCommands();
    
    if (!customCommands[commandName]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Command Not Found')
            .setDescription(`The command \`/${commandName}\` does not exist.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const command = customCommands[commandName];

    // Create modal for command editing
    const modal = new ModalBuilder()
        .setCustomId(`edit_command_modal_${commandName}`)
        .setTitle(`Edit Command: /${commandName}`);

    const descriptionInput = new TextInputBuilder()
        .setCustomId('command_description')
        .setLabel('Command Description')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Brief description of what this command does')
        .setValue(command.description)
        .setRequired(true)
        .setMaxLength(100);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('What the bot should respond with when this command is used')
        .setValue(command.response)
        .setRequired(true)
        .setMaxLength(2000);

    const firstActionRow = new ActionRowBuilder().addComponents(descriptionInput);
    const secondActionRow = new ActionRowBuilder().addComponents(responseInput);

    modal.addComponents(firstActionRow, secondActionRow);

    await interaction.showModal(modal);
}

// Handle delete command subcommand
async function handleDeleteCommand(interaction) {
    const commandName = interaction.options.getString('name');
    
    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to delete custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const result = await interaction.client.handleCustomCommands.deleteCustomCommand(commandName);

    const embed = new EmbedBuilder()
        .setColor(result.success ? '#4CAF50' : '#ff6b6b')
        .setTitle(result.success ? '✅ Command Deleted' : '❌ Deletion Failed')
        .setDescription(result.message)
        .setTimestamp();

    if (result.success) {
        embed.addFields({
            name: 'Deleted Command',
            value: `**Name:** \`/${commandName}\``,
            inline: false
        });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });
}
