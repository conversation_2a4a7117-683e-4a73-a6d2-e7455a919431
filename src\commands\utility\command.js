const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Path to message commands file
const MESSAGE_COMMANDS_FILE = path.join(__dirname, '../../data/messageCommands.json');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('command')
        .setDescription('Create and manage custom message commands (with !, +, or no prefix)')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Create a new custom message command'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('List all custom message commands'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('edit')
                .setDescription('Edit an existing custom message command')
                .addStringOption(option =>
                    option.setName('trigger')
                        .setDescription('The trigger word of the command to edit')
                        .setRequired(true)
                        .setAutocomplete(true))
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('The prefix of the command to edit')
                        .setRequired(true)
                        .addChoices(
                            { name: '! (exclamation)', value: '!' },
                            { name: '+ (plus)', value: '+' },
                            { name: 'none (no prefix)', value: 'none' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Delete a custom message command')
                .addStringOption(option =>
                    option.setName('trigger')
                        .setDescription('The trigger word of the command to delete')
                        .setRequired(true)
                        .setAutocomplete(true))
                .addStringOption(option =>
                    option.setName('prefix')
                        .setDescription('The prefix of the command to delete')
                        .setRequired(true)
                        .addChoices(
                            { name: '! (exclamation)', value: '!' },
                            { name: '+ (plus)', value: '+' },
                            { name: 'none (no prefix)', value: 'none' }
                        ))),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        switch (subcommand) {
            case 'create':
                await handleCreateCommand(interaction);
                break;
            case 'list':
                await handleListCommands(interaction);
                break;
            case 'edit':
                await handleEditCommand(interaction);
                break;
            case 'delete':
                await handleDeleteCommand(interaction);
                break;
        }
    },

    async autocomplete(interaction) {
        const focusedOption = interaction.options.getFocused(true);

        if (focusedOption.name === 'trigger') {
            const messageCommands = loadMessageCommands();
            const triggers = new Set();

            // Extract unique triggers from all commands
            Object.values(messageCommands).forEach(command => {
                triggers.add(command.trigger);
            });

            const choices = Array.from(triggers)
                .filter(trigger => trigger.toLowerCase().includes(focusedOption.value.toLowerCase()))
                .slice(0, 25)
                .map(trigger => ({ name: trigger, value: trigger }));

            await interaction.respond(choices);
        }
    }
};

// Helper functions for message commands

// Load message commands from JSON file
function loadMessageCommands() {
    try {
        if (!fs.existsSync(MESSAGE_COMMANDS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(MESSAGE_COMMANDS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading message commands:', error);
        return {};
    }
}

// Save message commands to JSON file
function saveMessageCommands(commands) {
    try {
        // Ensure the data directory exists
        const dataDir = path.dirname(MESSAGE_COMMANDS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(MESSAGE_COMMANDS_FILE, JSON.stringify(commands, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving message commands:', error);
        return false;
    }
}

// Handle create command subcommand
async function handleCreateCommand(interaction) {
    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to create custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Create modal for command creation
    const modal = new ModalBuilder()
        .setCustomId('create_message_command_modal')
        .setTitle('Create Custom Message Command');

    const prefixInput = new TextInputBuilder()
        .setCustomId('command_prefix')
        .setLabel('Command Prefix')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter: ! or + or none (for no prefix)')
        .setRequired(true)
        .setMaxLength(4);

    const triggerInput = new TextInputBuilder()
        .setCustomId('command_trigger')
        .setLabel('Command Trigger')
        .setStyle(TextInputStyle.Short)
        .setPlaceholder('Enter trigger word (e.g., hello, test, info)')
        .setRequired(true)
        .setMaxLength(32);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('What the bot should respond with. Use {user}, {username}, {server}, {channel} for placeholders')
        .setRequired(true)
        .setMaxLength(2000);

    const firstActionRow = new ActionRowBuilder().addComponents(prefixInput);
    const secondActionRow = new ActionRowBuilder().addComponents(triggerInput);
    const thirdActionRow = new ActionRowBuilder().addComponents(responseInput);

    modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);

    await interaction.showModal(modal);
}

// Handle list commands subcommand
async function handleListCommands(interaction) {
    const messageCommands = loadMessageCommands();
    const commandList = Object.entries(messageCommands);

    if (commandList.length === 0) {
        const embed = new EmbedBuilder()
            .setColor('#ffa500')
            .setTitle('📝 No Custom Message Commands')
            .setDescription('No custom message commands have been created yet. Use `/command create` to create one!')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const embed = new EmbedBuilder()
        .setColor('#4CAF50')
        .setTitle('📋 Custom Message Commands')
        .setDescription(`Found ${commandList.length} custom message command(s):`)
        .setTimestamp();

    commandList.forEach(([commandName, command], index) => {
        const createdDate = new Date(command.createdAt).toLocaleDateString();
        const usageCount = command.usageCount || 0;
        const prefixDisplay = command.prefix === 'none' ? 'no prefix' : `\`${command.prefix}\``;
        const usage = command.prefix === 'none' ? command.trigger : command.prefix + command.trigger;

        embed.addFields({
            name: `${index + 1}. ${usage}`,
            value: `**Prefix:** ${prefixDisplay}\n**Usage:** ${usageCount} times\n**Created:** ${createdDate}\n**Response:** ${command.response.substring(0, 50)}${command.response.length > 50 ? '...' : ''}`,
            inline: true
        });
    });

    await interaction.reply({ embeds: [embed], ephemeral: true });
}

// Handle edit command subcommand
async function handleEditCommand(interaction) {
    const trigger = interaction.options.getString('trigger');
    const prefix = interaction.options.getString('prefix');

    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to edit custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const messageCommands = loadMessageCommands();
    const commandName = `${prefix}_${trigger}`.toLowerCase();

    if (!messageCommands[commandName]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Command Not Found')
            .setDescription(`The command with trigger \`${trigger}\` and prefix \`${prefix}\` does not exist.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const command = messageCommands[commandName];

    // Create modal for command editing
    const modal = new ModalBuilder()
        .setCustomId(`edit_message_command_modal_${commandName}`)
        .setTitle(`Edit Command: ${prefix === 'none' ? trigger : prefix + trigger}`);

    const responseInput = new TextInputBuilder()
        .setCustomId('command_response')
        .setLabel('Command Response')
        .setStyle(TextInputStyle.Paragraph)
        .setPlaceholder('What the bot should respond with when this command is used')
        .setValue(command.response)
        .setRequired(true)
        .setMaxLength(2000);

    const actionRow = new ActionRowBuilder().addComponents(responseInput);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
}

// Handle delete command subcommand
async function handleDeleteCommand(interaction) {
    const trigger = interaction.options.getString('trigger');
    const prefix = interaction.options.getString('prefix');

    // Check permissions
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to delete custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    const messageCommands = loadMessageCommands();
    const commandName = `${prefix}_${trigger}`.toLowerCase();

    if (!messageCommands[commandName]) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Command Not Found')
            .setDescription(`The command with trigger \`${trigger}\` and prefix \`${prefix}\` does not exist.`)
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    delete messageCommands[commandName];

    if (saveMessageCommands(messageCommands)) {
        const embed = new EmbedBuilder()
            .setColor('#4CAF50')
            .setTitle('✅ Command Deleted')
            .setDescription('The custom message command has been deleted successfully.')
            .setTimestamp();

        embed.addFields({
            name: 'Deleted Command',
            value: `**Trigger:** \`${trigger}\`\n**Prefix:** ${prefix === 'none' ? 'no prefix' : `\`${prefix}\``}\n**Usage:** ${prefix === 'none' ? trigger : prefix + trigger}`,
            inline: false
        });

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } else {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Deletion Failed')
            .setDescription('Failed to delete the command. Please try again.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}
