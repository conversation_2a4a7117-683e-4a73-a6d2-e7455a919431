const { Events } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');
const fs = require('fs');
const path = require('path');

// Message tracking for points
const MESSAGES_FOR_POINTS = 100; // Messages needed to get points
const POINTS_PER_100_MESSAGES = 10; // Points awarded per 100 messages

// Path to message commands file
const MESSAGE_COMMANDS_FILE = path.join(__dirname, '../../data/messageCommands.json');

module.exports = {
    name: Events.MessageCreate,
    async execute(message) {
        if (message.author.bot) return; // Ignore bot messages

        // Handle team points for text activity
        await handleTextActivity(message);

        // Handle custom message commands (with prefixes or without)
        await handleMessageCommands(message);

        // Original Arabic greeting
        if (message.content.toLowerCase() === 'السلام عليكم') {
            message.reply('وعليكم السلام ورحمة الله وبركاته');
        };
          if (message.content.toLowerCase() === 'مساء الخير') {
            message.reply('مساء الورد');
        };
        if (message.content.toLowerCase() === 'صباح الخير') {
            message.reply('صباح الورد');
        }

        // Commands that start with !
        if (message.content.startsWith('!')) {
            const command = message.content.toLowerCase();

            switch (command) {

                case '!مساء الخير':
                    message.reply('مساء الورد');
                    break;
                    
                case '!صباح الخير':
                    message.reply('صباح الورد');
                    break;
                    
                case '!دعم':
                    message.reply('https://tip.dokan.sa/sahm');
                    break;

                case '!x':
                    message.reply('https://x.com/4e56');
                    break;

                case '!اكس':
                    message.reply('https://x.com/4e56');
                    break;

                case '!تويتر':
                    message.reply('https://x.com/4e56');
                    break;

                case '!كيك':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!kick':
                    message.reply('https://kick.com/ssahm');
                    break;

                case '!حسابات':
                    message.reply('https://guns.lol/.sahm');
                    break;

                case '!ص':
                    message.reply('اللهم صل وسلم على سيدنا محمد');
                    break;

                    
                case '!كفاره':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  

                case '!كفارة':
                    message.reply('سُبْحَانَكَ اللّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لا إِلَهَ إِلاَّ أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ.');
                    break;  
                                       
                case '!احبك':
                    message.reply('ماحبيت الا اللي يحبك ويغليك');
                    break;  
                    
                        
                case '!س':
                    message.reply('سبحان الله وبحمده سبحان الله العظيم');
                    break;
                    
                case '!صلاه':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;

                case '!صلاة':
                    message.channel.send(' الصلاة يا عباد الله، اللي ما صلى يصلي');
                    break;
            }
        }
    }
};

async function handleTextActivity(message) {
    try {
        // Excluded channels - no points awarded for messages in these channels
        const excludedChannels = [
            '1347454380093341758',
            '1325734703155904533',
            '1386926777120850053'
        ];

        // Skip point tracking for excluded channels
        if (excludedChannels.includes(message.channel.id)) {
            return; // Exit early, no points for these channels
        }

        const userId = message.author.id;

        // Get current message count from database
        const teamManager = getTeamManager();
        const currentCount = teamManager.db.getMessageCount(userId);
        const newCount = currentCount + 1;

        // Update message count in database
        teamManager.db.updateMessageCount(userId, newCount);

        // Check if user reached 100 messages milestone
        if (newCount % MESSAGES_FOR_POINTS === 0) {
            // Award points for reaching 100 messages
            const result = await teamManager.addPointsToMember(userId, POINTS_PER_100_MESSAGES, 'Text Chat Milestone');

            if (result.success) {
                // Log milestone achievement
                console.log(`🎉 ${message.author.tag} reached ${newCount} messages milestone and earned ${POINTS_PER_100_MESSAGES} points!`);
            }
        }

    } catch (error) {
        console.error('Error handling text activity:', error);
    }
}

// Message count tracking is now stored in database for persistence

// Helper functions for message commands

// Load message commands from JSON file
function loadMessageCommands() {
    try {
        if (!fs.existsSync(MESSAGE_COMMANDS_FILE)) {
            return {};
        }
        const data = fs.readFileSync(MESSAGE_COMMANDS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading message commands:', error);
        return {};
    }
}

// Save message commands to JSON file
function saveMessageCommands(commands) {
    try {
        // Ensure the data directory exists
        const dataDir = path.dirname(MESSAGE_COMMANDS_FILE);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(MESSAGE_COMMANDS_FILE, JSON.stringify(commands, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving message commands:', error);
        return false;
    }
}

// Handle custom message commands
async function handleMessageCommands(message) {
    try {
        const messageCommands = loadMessageCommands();
        const content = message.content.toLowerCase();

        // Check for commands with different prefixes
        for (const [commandName, commandData] of Object.entries(messageCommands)) {
            const { trigger, response, prefix } = commandData;

            let shouldTrigger = false;

            // Check based on prefix type
            switch (prefix) {
                case '!':
                    shouldTrigger = content === `!${trigger.toLowerCase()}`;
                    break;
                case '+':
                    shouldTrigger = content === `+${trigger.toLowerCase()}`;
                    break;
                case 'none':
                    shouldTrigger = content === trigger.toLowerCase();
                    break;
                default:
                    shouldTrigger = content === `${prefix}${trigger.toLowerCase()}`;
                    break;
            }

            if (shouldTrigger) {
                // Replace placeholders in response
                let finalResponse = response;
                finalResponse = finalResponse.replace(/\{user\}/g, `<@${message.author.id}>`);
                finalResponse = finalResponse.replace(/\{username\}/g, message.author.username);
                finalResponse = finalResponse.replace(/\{server\}/g, message.guild.name);
                finalResponse = finalResponse.replace(/\{channel\}/g, `<#${message.channel.id}>`);

                // Update usage count
                commandData.usageCount = (commandData.usageCount || 0) + 1;
                commandData.lastUsed = Date.now();
                messageCommands[commandName] = commandData;
                saveMessageCommands(messageCommands);

                // Send response
                await message.reply(finalResponse);
                return; // Exit after first match
            }
        }
    } catch (error) {
        console.error('Error handling message commands:', error);
    }
}


